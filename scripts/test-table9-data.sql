-- 测试表9（新增表）数据分析脚本
-- 作者: Claude
-- 日期: 2025-07-26
-- 目的: 深入分析新增表中的数据，找出表9导出无数据的原因

-- ======================================
-- 1. 检查新增表中是否有2025年的数据
-- ======================================
SELECT 
    '=== 新增表2025年数据统计 ===' AS '查询说明';
    
SELECT 
    年份,
    COUNT(*) AS 记录数,
    COUNT(DISTINCT 债权人) AS 债权人数,
    COUNT(DISTINCT 债务人) AS 债务人数,
    COUNT(DISTINCT 期间) AS 期间数
FROM 新增表
WHERE 年份 = '2025'
GROUP BY 年份;

-- ======================================
-- 2. 查看2025年各月份字段的数据分布
-- ======================================
SELECT 
    '=== 2025年各月份数据分布（非零值统计） ===' AS '查询说明';

SELECT 
    期间,
    COUNT(CASE WHEN `1月` <> 0 THEN 1 END) AS '1月非零记录',
    COUNT(CASE WHEN `2月` <> 0 THEN 1 END) AS '2月非零记录',
    COUNT(CASE WHEN `3月` <> 0 THEN 1 END) AS '3月非零记录',
    COUNT(CASE WHEN `4月` <> 0 THEN 1 END) AS '4月非零记录',
    COUNT(CASE WHEN `5月` <> 0 THEN 1 END) AS '5月非零记录',
    COUNT(CASE WHEN `6月` <> 0 THEN 1 END) AS '6月非零记录',
    COUNT(CASE WHEN `7月` <> 0 THEN 1 END) AS '7月非零记录',
    COUNT(CASE WHEN `8月` <> 0 THEN 1 END) AS '8月非零记录',
    COUNT(CASE WHEN `9月` <> 0 THEN 1 END) AS '9月非零记录',
    COUNT(CASE WHEN `10月` <> 0 THEN 1 END) AS '10月非零记录',
    COUNT(CASE WHEN `11月` <> 0 THEN 1 END) AS '11月非零记录',
    COUNT(CASE WHEN `12月` <> 0 THEN 1 END) AS '12月非零记录',
    COUNT(*) AS 总记录数
FROM 新增表
WHERE 年份 = '2025'
GROUP BY 期间;

-- ======================================
-- 3. 查看期间字段的实际值分布
-- ======================================
SELECT 
    '=== 期间字段值分布 ===' AS '查询说明';

SELECT DISTINCT 
    期间,
    COUNT(*) AS 记录数
FROM 新增表
WHERE 年份 = '2025'
GROUP BY 期间
ORDER BY 期间;

-- ======================================
-- 4. 检查是否有任何一个月份字段有非零值
-- ======================================
SELECT 
    '=== 检查是否有任何月份字段非零（2025年） ===' AS '查询说明';

SELECT 
    债权人,
    债务人,
    期间,
    是否涉诉,
    CASE 
        WHEN `1月` <> 0 OR `2月` <> 0 OR `3月` <> 0 OR 
             `4月` <> 0 OR `5月` <> 0 OR `6月` <> 0 OR 
             `7月` <> 0 OR `8月` <> 0 OR `9月` <> 0 OR 
             `10月` <> 0 OR `11月` <> 0 OR `12月` <> 0 
        THEN '有非零值'
        ELSE '全部为零'
    END AS 月份数据状态,
    `1月`, `2月`, `3月`, `4月`, `5月`, `6月`, 
    `7月`, `8月`, `9月`, `10月`, `11月`, `12月`,
    新增金额,
    备注
FROM 新增表
WHERE 年份 = '2025'
LIMIT 20;

-- ======================================
-- 5. 统计有非零月份值的记录数
-- ======================================
SELECT 
    '=== 有非零月份值的记录统计 ===' AS '查询说明';

SELECT 
    COUNT(*) AS 总记录数,
    COUNT(CASE 
        WHEN `1月` <> 0 OR `2月` <> 0 OR `3月` <> 0 OR 
             `4月` <> 0 OR `5月` <> 0 OR `6月` <> 0 OR 
             `7月` <> 0 OR `8月` <> 0 OR `9月` <> 0 OR 
             `10月` <> 0 OR `11月` <> 0 OR `12月` <> 0 
        THEN 1 
    END) AS 有非零月份值的记录数
FROM 新增表
WHERE 年份 = '2025';

-- ======================================
-- 6. 分析NULL值情况
-- ======================================
SELECT 
    '=== NULL值分析 ===' AS '查询说明';

SELECT 
    期间,
    SUM(CASE WHEN `1月` IS NULL THEN 1 ELSE 0 END) AS '1月NULL数',
    SUM(CASE WHEN `2月` IS NULL THEN 1 ELSE 0 END) AS '2月NULL数',
    SUM(CASE WHEN `3月` IS NULL THEN 1 ELSE 0 END) AS '3月NULL数',
    SUM(CASE WHEN `4月` IS NULL THEN 1 ELSE 0 END) AS '4月NULL数',
    SUM(CASE WHEN `5月` IS NULL THEN 1 ELSE 0 END) AS '5月NULL数',
    SUM(CASE WHEN `6月` IS NULL THEN 1 ELSE 0 END) AS '6月NULL数',
    COUNT(*) AS 总记录数
FROM 新增表
WHERE 年份 = '2025'
GROUP BY 期间;

-- ======================================
-- 7. 测试表9查询的WHERE条件
-- ======================================
SELECT 
    '=== 模拟表9查询WHERE条件的效果 ===' AS '查询说明';

-- 模拟原始WHERE条件
SELECT 
    债权人,
    债务人,
    是否涉诉,
    期间,
    科目名称,
    债权性质,
    IFNULL(`1月`, 0) AS `1月`,
    IFNULL(`2月`, 0) AS `2月`,
    '原WHERE条件' AS 查询类型
FROM 新增表
WHERE 年份 = '2025'
  AND (`1月` <> 0 OR `2月` <> 0 OR `3月` <> 0 OR 
       `4月` <> 0 OR `5月` <> 0 OR `6月` <> 0 OR 
       `7月` <> 0 OR `8月` <> 0 OR `9月` <> 0 OR 
       `10月` <> 0 OR `11月` <> 0 OR `12月` <> 0)
LIMIT 10;

-- ======================================
-- 8. 处置表关联数据检查
-- ======================================
SELECT 
    '=== 处置表2025年数据检查 ===' AS '查询说明';

SELECT 
    年份,
    月份,
    COUNT(*) AS 记录数,
    SUM(现金处置 + 分期还款 + 资产抵债 + 其他方式) AS 累计处置金额
FROM 处置表
WHERE 年份 = 2025 AND 月份 <= 2
GROUP BY 年份, 月份;

-- ======================================
-- 9. 检查所有年份的数据分布
-- ======================================
SELECT 
    '=== 新增表所有年份数据分布 ===' AS '查询说明';

SELECT 
    年份,
    COUNT(*) AS 记录数,
    COUNT(CASE 
        WHEN `1月` <> 0 OR `2月` <> 0 OR `3月` <> 0 OR 
             `4月` <> 0 OR `5月` <> 0 OR `6月` <> 0 OR 
             `7月` <> 0 OR `8月` <> 0 OR `9月` <> 0 OR 
             `10月` <> 0 OR `11月` <> 0 OR `12月` <> 0 
        THEN 1 
    END) AS 有非零月份值的记录数
FROM 新增表
GROUP BY 年份
ORDER BY 年份;

-- ======================================
-- 10. 查看具体的数据示例
-- ======================================
SELECT 
    '=== 2025年新增表数据示例（前10条） ===' AS '查询说明';

SELECT 
    债权人,
    债务人,
    期间,
    是否涉诉,
    年份,
    科目名称,
    债权性质,
    `1月`, `2月`, `3月`, `4月`, `5月`, `6月`,
    新增金额,
    备注
FROM 新增表
WHERE 年份 = '2025'
LIMIT 10;