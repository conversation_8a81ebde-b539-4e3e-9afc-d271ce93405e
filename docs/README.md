# 财务系统文档中心

欢迎来到财务系统的文档中心！这里包含了项目的所有技术文档和指南。

## 📋 文档导航

### 📖 项目指南
- [项目完整指南](guides/README.md) - 项目概览和快速入门
- [项目目录结构说明](guides/项目目录结构说明.md) - 详细的项目目录结构说明 🆕
- [项目当前状态总结](guides/project-current-status.md) - 项目现状和运行状态
- [comprehensive项目改进计划](guides/comprehensive-project-improvement-plan.md) - 深入全面的项目改进分析和实施计划
- [AI助手工作指南](guides/ai-assistant-guide.md) - AI助手使用规范和项目信息
- [用户偏好与项目规则](guides/user-preferences-and-project-rules.md) - 详细的用户偏好记录
- [项目重要内容](guides/项目重要内容.md) - 核心业务和技术要点
- [项目知识记忆库](guides/memories.md) - 项目知识归档


### 💻 开发相关
- [开发指南](development/README.md) - 开发环境搭建、编码规范、开发流程
- [API接口文档](api/README.md) - REST API接口规范和使用方法

### 🚀 部署相关
- [部署指南](deployment/README.md) - 完整的部署流程和CI/CD自动化

### 💼 业务文档
- [用户系统数据库迁移记录](business/用户系统数据库迁移与问题修复记录.md) - 数据库迁移详情
- [后续月份更新逻辑分析](business/后续月份更新逻辑分析.md) - 业务逻辑分析
- [前端字段映射分析](business/前端字段与后端表字段映射分析.md) - 数据映射关系

### 🔧 运维相关
- [运维操作文档](operations/README.md) - 运维操作指南和检查清单
- [MySQL双向复制](operations/mysql-bidirectional-replication.md) - MySQL双向复制原理、配置和监控
- [数据库迁移](operations/database-migration.md) - 数据库迁移操作记录
- [部署成功报告](operations/deployment-success-report.md) - Docker容器化部署报告
- [项目结构清理报告](operations/项目结构清理报告.md) - 项目结构优化清理记录 🆕

### 🛠️ 故障排除
- [故障排除指南](troubleshooting/README.md) - 常见问题和解决方案

### 🤖 Claude Code 用户
- [Claude 配置文档](.claude/docs/) - AI助手专用的结构化参考文档
- [Claude 快速参考](.claude/docs/README.md) - AI助手文档导航和使用说明
- [项目架构参考](.claude/docs/architecture.md) - AI助手专用的架构概览
- [API 端点参考](.claude/docs/api-endpoints.md) - AI助手专用的API快速参考

### 📚 项目历史
- [变更日志](CHANGELOG.md) - 项目版本历史和重要变更记录
- [项目状态总结](project-status-summary.md) - 项目当前状态和完成度

### 📚 归档文档
- [部署历史](archive/deployment-history/) - 历史部署记录
- [项目结构整理方案](archive/项目结构整理方案.md) - 历史整理方案
- [项目结构重构方案](archive/项目结构重构方案.md) - 过时的重构方案
- [Repository SQL分析](archive/FinancialSystem_Repository_SQL_Analysis.md) - 技术分析报告
- [Docker镜像部署包说明](archive/README-docker-images-obsolete.txt) - 过时的Docker镜像部署说明

## 🔍 快速查找

### 我想要...
- **了解项目整体情况** → [项目当前状态总结](guides/project-current-status.md)
- **了解项目目录结构** → [项目目录结构说明](guides/项目目录结构说明.md) 🆕
- **快速上手项目** → [项目完整指南](guides/README.md)
- **了解AI助手使用** → [AI助手工作指南](guides/ai-assistant-guide.md)
- **了解用户偏好** → [用户偏好与项目规则](guides/user-preferences-and-project-rules.md)
- **解决技术问题** → [故障排除](troubleshooting/)
- **了解业务逻辑** → [业务文档](business/)
- **查看历史记录** → [归档文档](archive/)

### 按角色查找
- **新开发者** → [开发指南](development/README.md) → [项目完整指南](guides/README.md)
- **前端开发者** → [开发指南](development/README.md) → [API接口文档](api/README.md)
- **后端开发者** → [开发指南](development/README.md) → [故障排除指南](troubleshooting/README.md)
- **AI助手** → [AI助手工作指南](guides/ai-assistant-guide.md) → [Claude 配置文档](../.claude/docs/)
- **项目维护者** → [项目当前状态](guides/project-current-status.md) → [项目重要内容](guides/项目重要内容.md)
- **业务人员** → [业务文档](business/) → [数据库迁移记录](business/用户系统数据库迁移与问题修复记录.md)
- **运维人员** → [运维文档](operations/) → [部署成功报告](operations/deployment-success-report.md)

## 📝 文档贡献

如果您需要添加或更新文档，请遵循以下规范：

1. **文档格式**: 使用Markdown格式
2. **命名规范**: 使用小写字母和连字符
3. **目录结构**: 按功能分类放置
4. **更新索引**: 在相应的README.md中添加链接

## 🆕 最近更新

- **2025-07-06**:
  - ✅ 完成文档结构深度整理和优化
  - ✅ 移除过时和重复的文档内容
  - ✅ 更新项目状态总结，反映最新完成度(95%)
  - ✅ 重新组织归档文档，保持文档目录清洁
  - ✅ 统一文档标准，提升文档质量和可维护性
- **2025-06-30**: 完成项目结构深度清理和优化，Redis依赖问题修复
- **2025-06-23**: 创建AI助手工作指南和用户偏好记录，文档结构整理
- **2025-06-22**: 完成Docker部署配置和CI/CD自动化

---

**文档维护者**: FinancialSystem开发团队 + AI Assistant (Augment Agent)
*如有疑问，请查看对应的文档分类或联系开发团队。*
