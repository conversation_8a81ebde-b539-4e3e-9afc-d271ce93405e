# 债权转换系统功能说明

## 系统概述

债权转换系统是FinancialSystem的核心功能模块之一，提供诉讼与非诉讼债权相互转换的完整解决方案。该系统严格遵循业务规则，确保数据一致性和操作的可追溯性。

## 功能特性

### 1. 债权记录搜索
- **智能搜索**: 支持按债权人、债务人或两者组合进行搜索
- **实时过滤**: 根据转换方向自动过滤可转换的记录
- **多数据源**: 同时搜索诉讼表和非诉讼表数据
- **状态显示**: 清晰显示债权当前状态（诉讼/非诉讼）

### 2. 双向转换支持
- **诉讼转非诉讼**: 将诉讼债权转换为非诉讼债权
- **非诉讼转诉讼**: 将非诉讼债权转换为诉讼债权
- **事务保证**: 使用数据库事务确保转换过程的原子性
- **数据完整性**: 同时更新三个核心表（诉讼表、非诉讼表、减值准备表）

### 3. 业务逻辑验证
- **前端验证**: 实时验证用户输入的完整性和正确性
- **后端验证**: 严格的业务规则验证和数据约束检查
- **状态匹配**: 确保转换方向与记录状态匹配
- **必填字段**: 非诉讼转诉讼时强制要求诉讼案件名称

## 技术架构

### 后端组件

#### 1. Controller层
- **文件**: `DebtConversionController.java`
- **功能**: 提供REST API接口，处理HTTP请求
- **端点**:
  - `GET /api/debts/conversion/search` - 搜索可转换债权
  - `POST /api/debts/conversion/litigation-to-non-litigation` - 诉讼转非诉讼
  - `POST /api/debts/conversion/non-litigation-to-litigation` - 非诉讼转诉讼

#### 2. Service层
- **文件**: `DebtConversionService.java`
- **功能**: 核心业务逻辑实现
- **特性**:
  - 事务管理（@Transactional）
  - 详细日志记录
  - 异常处理和回滚
  - 数据转换和映射

#### 3. DTO层
- **DebtConversionRequestDTO**: 转换请求数据传输对象
- **DebtConversionResponseDTO**: 转换响应数据传输对象
- **DebtSearchResultDTO**: 搜索结果数据传输对象

### 前端组件

#### 1. 主要组件
- **文件**: `LitigationConversion.js`
- **功能**: 用户界面和交互逻辑
- **特性**:
  - 响应式设计
  - 实时搜索
  - 表单验证
  - 状态管理

#### 2. API调用模块
- **文件**: `debtConversionApi.js`
- **功能**: 封装后端API调用
- **特性**:
  - 统一的错误处理
  - 请求验证
  - 响应格式化

## 数据流程

### 搜索流程
1. 用户输入债权人/债务人信息
2. 前端实时调用搜索API
3. 后端查询诉讼表和非诉讼表
4. 返回格式化的搜索结果
5. 前端显示可选择的记录

### 转换流程
1. 用户选择要转换的记录
2. 填写转换信息（年月、备注等）
3. 提交转换请求
4. 后端执行事务操作：
   - 验证数据完整性
   - 更新源表（清零相关字段）
   - 创建目标表记录
   - 更新减值准备表
5. 返回转换结果

## 业务规则

### 1. 诉讼转非诉讼
- **源数据**: 诉讼表记录
- **操作**: 
  - 清零诉讼表的诉讼本金、诉讼利息、本月债权余额
  - 在非诉讼表创建新记录，金额设置为原诉讼债权余额
  - 将减值准备表的"是否涉诉"从"是"改为"否"

### 2. 非诉讼转诉讼
- **源数据**: 非诉讼表记录
- **操作**:
  - 清零非诉讼表的本月本金、利息、违约金
  - 在诉讼表创建新记录，设置诉讼相关信息
  - 将减值准备表的"是否涉诉"从"否"改为"是"
- **必填**: 诉讼案件名称

### 3. 数据一致性
- **复合主键**: 债权人+债务人+期间+年份+月份
- **金额转换**: 确保转换前后总金额一致
- **备注记录**: 自动添加转换记录到备注字段
- **时间戳**: 记录转换的具体时间

## API文档

### 搜索债权记录
```http
GET /api/debts/conversion/search?creditor={债权人}&debtor={债务人}
```

**响应示例**:
```json
[
  {
    "creditor": "中国银行",
    "debtor": "某某公司",
    "period": "2025年新增债权",
    "debtBalance": 1000000.00,
    "currentStatus": "诉讼",
    "isLitigation": "是",
    "managementCompany": "某某管理公司",
    "subjectName": "短期借款",
    "year": 2025,
    "month": 1,
    "sourceTable": "litigation",
    "litigationCase": "某某债权纠纷案"
  }
]
```

### 诉讼转非诉讼
```http
POST /api/debts/conversion/litigation-to-non-litigation
Content-Type: application/json

{
  "creditor": "中国银行",
  "debtor": "某某公司",
  "period": "2025年新增债权",
  "year": 2025,
  "month": 1,
  "conversionYear": 2025,
  "conversionMonth": 7,
  "remark": "转换原因说明"
}
```

### 非诉讼转诉讼
```http
POST /api/debts/conversion/non-litigation-to-litigation
Content-Type: application/json

{
  "creditor": "中国银行",
  "debtor": "某某公司",
  "period": "2025年新增债权",
  "year": 2025,
  "month": 1,
  "conversionYear": 2025,
  "conversionMonth": 7,
  "litigationCase": "某某公司债权纠纷案",
  "litigationOccurredPrincipal": 1000000.00,
  "litigationInterestFee": 50000.00,
  "litigationFee": 15000.00,
  "intermediaryFee": 10000.00,
  "remark": "转换原因说明"
}
```

## 安全性考虑

### 1. 权限控制
- JWT认证保护所有API端点
- 基于角色的访问控制
- 审计日志记录所有操作

### 2. 数据验证
- 前端实时验证
- 后端严格验证
- 数据库约束检查

### 3. 事务安全
- 数据库事务保证一致性
- 异常回滚机制
- 并发控制

## 错误处理

### 1. 常见错误
- 记录不存在
- 状态不匹配
- 必填字段缺失
- 数据格式错误

### 2. 错误响应格式
```json
{
  "success": false,
  "message": "错误描述",
  "details": "详细错误信息",
  "timestamp": 1627776000000
}
```

## 测试指南

### 1. 单元测试
- Service层业务逻辑测试
- Controller层接口测试
- DTO验证测试

### 2. 集成测试
- 完整转换流程测试
- 数据库事务测试
- API端到端测试

### 3. 用户测试
- 前端界面功能测试
- 用户体验测试
- 异常场景测试

## 部署说明

### 1. 后端部署
- 确保数据库连接配置正确
- 验证事务管理器配置
- 检查日志配置

### 2. 前端部署
- 配置正确的API基础URL
- 确保认证令牌处理正确
- 验证路由配置

## 维护和监控

### 1. 日志监控
- 转换操作日志
- 错误异常日志
- 性能指标监控

### 2. 数据监控
- 转换操作统计
- 数据一致性检查
- 业务指标分析

## 扩展性

### 1. 未来增强
- 批量转换功能
- 转换历史查询
- 自动化转换规则
- 更多债权类型支持

### 2. 系统集成
- 与工作流系统集成
- 与通知系统集成
- 与报表系统集成

---

*该文档描述了债权转换系统的完整功能和技术实现，为开发、测试、部署和维护提供全面指导。*