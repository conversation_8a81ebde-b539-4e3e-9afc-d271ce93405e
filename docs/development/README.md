# 开发指南

本文档提供FinancialSystem项目的开发环境搭建、编码规范和开发流程指导。

## 🛠️ 开发环境搭建

### 系统要求
- **Java**: JDK 21 (LTS)
- **Maven**: 3.9+
- **Node.js**: 18+
- **MySQL**: 8.0+
- **Docker**: 最新版本 (可选)
- **IDE**: IntelliJ IDEA 或 VS Code

### 环境配置

#### 1. Java环境
```bash
# 验证Java版本
java -version
# 应显示: openjdk version "21.x.x"

# 设置JAVA_HOME
export JAVA_HOME=/path/to/jdk-21
export PATH=$JAVA_HOME/bin:$PATH
```

#### 2. 数据库配置
```sql
-- 创建主数据库
CREATE DATABASE `逾期债权数据库` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建金蝶数据库 (可选)
CREATE DATABASE `kingdee` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户
CREATE USER 'root'@'localhost' IDENTIFIED BY 'Zlb&198838';
GRANT ALL PRIVILEGES ON *.* TO 'root'@'localhost';
FLUSH PRIVILEGES;
```

#### 3. 项目克隆和构建
```bash
# 克隆项目
git clone <repository-url>
cd FinancialSystem

# 编译项目
mvn clean compile

# 启动后端 (从api-gateway模块)
cd api-gateway
mvn spring-boot:run

# 启动前端 (新终端)
cd FinancialSystem-web
npm install
npm start
```

### 快速启动脚本

为了简化开发流程，项目提供了快速启动脚本：

```bash
# 初始化数据库（首次运行）
./scripts/init-local-db.sh

# 启动应用
./scripts/start-local.sh

# 停止应用
./scripts/stop-local.sh
```

### 访问地址
- **前端**: http://localhost:3000
- **后端API**: http://localhost:8080/api
- **健康检查**: http://localhost:8080/actuator/health

### 常见问题排查

#### 后端启动失败
1. **检查Java版本**: `java -version`
2. **检查MySQL是否运行**: `mysql -u root -p`
3. **查看日志**: `tail -f logs/backend.log`

#### 前端启动失败
1. **检查Node版本**: `node -v`
2. **清理缓存**: `rm -rf node_modules package-lock.json && npm install`
3. **查看日志**: `tail -f logs/frontend.log`

#### 前后端连接失败
1. **检查后端是否启动**: `curl http://localhost:8080/actuator/health`
2. **检查CORS配置**: 查看浏览器控制台错误
3. **检查代理配置**: `FinancialSystem-web/src/setupProxy.js`

### 手动启动方式

```bash
# 启动后端
cd api-gateway
mvn spring-boot:run -Dspring.profiles.active=local

# 启动前端
cd FinancialSystem-web
npm start
```

### 环境配置文件
- **前端环境变量**: `FinancialSystem-web/.env.local`
- **后端本地配置**: `api-gateway/src/main/resources/application-local.yml`

### 开发常见问题

#### Q: 如何修改后端端口？
A: 编辑 `application-local.yml` 中的 `server.port`

#### Q: 如何修改前端端口？
A: 在 `.env.local` 中设置 `PORT=3001`

#### Q: 如何启用调试模式？
A: 后端使用 `mvn spring-boot:run -Dspring-boot.run.jvmArguments="-Xdebug -Xrunjdwp:transport=dt_socket,server=y,suspend=n,address=5005"`

## 📝 编码规范

### Java编码规范
- **代码风格**: Google Java Style Guide
- **命名规范**: 
  - 类名: PascalCase (如 `UserService`)
  - 方法名: camelCase (如 `getUserById`)
  - 常量: UPPER_SNAKE_CASE (如 `MAX_RETRY_COUNT`)
- **注释规范**: 使用JavaDoc格式
- **包结构**: 按功能模块组织

### 前端编码规范
- **代码风格**: Airbnb JavaScript Style Guide
- **组件命名**: PascalCase
- **文件命名**: kebab-case
- **状态管理**: 使用React Hooks

### 数据库规范
- **表名**: 中文名称 (如 `新增表`, `诉讼表`)
- **字段名**: 中文名称 (如 `债权人`, `债务人`)
- **索引**: 为查询字段创建适当索引
- **外键**: 明确定义外键关系

## 🏗️ 项目架构

### 模块结构
```
FinancialSystem/
├── api-gateway/              # Web服务层 (主启动模块)
├── FinancialSystem-web/      # 前端应用
├── services/                 # 业务模块层
├── shared/                   # 共享模块
├── integrations/            # 第三方集成
└── business-modules/        # 业务模块配置
```

### 开发流程

#### 1. 新功能开发
1. **创建分支**: `git checkout -b feature/功能名称`
2. **编写代码**: 遵循编码规范
3. **单元测试**: 编写并运行测试
4. **集成测试**: 验证与其他模块的集成
5. **代码审查**: 提交Pull Request
6. **合并代码**: 审查通过后合并到main分支

#### 2. Bug修复
1. **创建分支**: `git checkout -b bugfix/问题描述`
2. **问题定位**: 使用调试工具定位问题
3. **编写修复**: 最小化修改范围
4. **回归测试**: 确保修复不影响其他功能
5. **提交修复**: 提交Pull Request

## 🧪 测试策略

### 单元测试
- **框架**: JUnit 5 + Mockito
- **覆盖率**: 目标 > 80%
- **命名**: `should_ExpectedBehavior_When_StateUnderTest`

```java
@Test
void should_ReturnUser_When_ValidIdProvided() {
    // Given
    Long userId = 1L;
    User expectedUser = new User();
    when(userRepository.findById(userId)).thenReturn(Optional.of(expectedUser));
    
    // When
    User result = userService.getUserById(userId);
    
    // Then
    assertThat(result).isEqualTo(expectedUser);
}
```

### 集成测试
- **框架**: Spring Boot Test
- **数据库**: 使用TestContainers或H2
- **范围**: 测试模块间交互

### 前端测试
- **框架**: Jest + React Testing Library
- **类型**: 组件测试、集成测试
- **覆盖**: 关键业务流程

## 🔧 开发工具

### IDE配置
- **IntelliJ IDEA**: 推荐插件
  - Lombok Plugin
  - SonarLint
  - GitToolBox
  - Database Navigator

### 代码质量
- **静态分析**: SonarQube
- **代码格式化**: Prettier (前端), Google Java Format (后端)
- **Git Hooks**: pre-commit检查

## 🚀 部署流程

### 本地部署
```bash
# 使用Docker Compose
docker-compose up -d

# 手动部署
./scripts/deploy/local-deploy.sh
```

### 生产部署
- **自动化**: 合并到main分支自动触发CI/CD
- **手动**: 使用部署脚本
- **回滚**: 支持快速回滚到上一版本

## 📚 参考资源

### 内部文档
- [项目完整指南](../guides/README.md)
- [AI助手工作指南](../guides/ai-assistant-guide.md)
- [故障排除指南](../troubleshooting/README.md)

### 外部资源
- [Spring Boot官方文档](https://spring.io/projects/spring-boot)
- [React官方文档](https://reactjs.org/docs)
- [MySQL官方文档](https://dev.mysql.com/doc/)

## 🆘 获取帮助

### 内部支持
- **技术问题**: 查看[故障排除指南](../troubleshooting/README.md)
- **业务问题**: 查看[业务文档](../business/)
- **架构问题**: 查看[项目完整指南](../guides/README.md)

### 联系方式
- **项目负责人**: Zhou Libing
- **AI助手**: Augment Agent
- **紧急联系**: 查看项目README.md

---

**最后更新**: 2025-06-23  
**维护者**: FinancialSystem开发团队
