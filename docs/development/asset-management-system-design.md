# 资产管理系统设计文档

## 1. 系统概述

资产管理系统主要用于管理企业的房产和土地资产，包括资产基本信息、状态管理、财务信息和相关文件管理。系统支持多维度的数据展示和分析。

## 2. 功能模块

### 2.1 资产基本信息模块
- 资产名称、产权人、权属证号、获取时间
- 购买合同、购买价格、位置、总面积
- 产权年限、已使用年限、剩余使用年限

### 2.2 资产状态模块
- 面积分配：自用面积、出租面积、闲置面积
- 出租信息管理：出租人、承租人、合同信息、租金等

### 2.3 资产财务信息模块
- 入账时间、入账原值、折旧年限、折旧方法
- 残值率、账面价值
- 支持按月份筛选财务信息

### 2.4 数据可视化模块
- 圆饼图：资产总面积、自用面积、出租面积分布
- 柱形图：各公司资产盘活情况和比率
- 瑕疵资产统计

## 3. 数据库表结构设计

### 3.1 资产基本信息表 (asset_basic_info)
```sql
CREATE TABLE asset_basic_info (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    asset_name VARCHAR(200) NOT NULL COMMENT '资产名称',
    property_owner VARCHAR(100) NOT NULL COMMENT '产权人',
    property_certificate_no VARCHAR(100) COMMENT '权属证号',
    acquisition_date DATE COMMENT '获取时间',
    purchase_contract VARCHAR(200) COMMENT '购买合同编号',
    purchase_price DECIMAL(20,2) COMMENT '购买价格',
    location VARCHAR(500) COMMENT '位置',
    total_area DECIMAL(15,2) NOT NULL COMMENT '总面积(平方米)',
    property_years INT COMMENT '产权年限',
    used_years INT COMMENT '已使用年限',
    remaining_years INT COMMENT '剩余使用年限',
    asset_type ENUM('PROPERTY', 'LAND') NOT NULL COMMENT '资产类型：房产/土地',
    status ENUM('ACTIVE', 'INACTIVE', 'DISPOSED') DEFAULT 'ACTIVE' COMMENT '资产状态',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    created_by VARCHAR(50) COMMENT '创建人',
    updated_by VARCHAR(50) COMMENT '更新人',
    INDEX idx_asset_name (asset_name),
    INDEX idx_property_owner (property_owner),
    INDEX idx_asset_type (asset_type)
) COMMENT='资产基本信息表';
```

### 3.2 资产状态表 (asset_status)
```sql
CREATE TABLE asset_status (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    asset_id BIGINT NOT NULL COMMENT '资产ID',
    self_use_area DECIMAL(15,2) DEFAULT 0 COMMENT '自用面积',
    rental_area DECIMAL(15,2) DEFAULT 0 COMMENT '出租面积',
    idle_area DECIMAL(15,2) DEFAULT 0 COMMENT '闲置面积',
    status_date DATE NOT NULL COMMENT '状态日期',
    remark TEXT COMMENT '备注',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (asset_id) REFERENCES asset_basic_info(id) ON DELETE CASCADE,
    INDEX idx_asset_id (asset_id),
    INDEX idx_status_date (status_date),
    UNIQUE KEY uk_asset_status_date (asset_id, status_date)
) COMMENT='资产状态表';
```

### 3.3 出租信息表 (rental_info)
```sql
CREATE TABLE rental_info (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    asset_id BIGINT NOT NULL COMMENT '资产ID',
    lessor VARCHAR(100) NOT NULL COMMENT '出租人',
    lessee VARCHAR(100) NOT NULL COMMENT '承租人',
    contract_sign_date DATE COMMENT '合同签订时间',
    rental_start_date DATE NOT NULL COMMENT '出租起始日期',
    rental_end_date DATE NOT NULL COMMENT '出租截止日期',
    rental_area DECIMAL(15,2) NOT NULL COMMENT '出租面积',
    rental_price DECIMAL(15,2) NOT NULL COMMENT '出租价格(月租)',
    payment_method VARCHAR(50) COMMENT '付款方式',
    contract_status ENUM('ACTIVE', 'EXPIRED', 'TERMINATED') DEFAULT 'ACTIVE' COMMENT '合同状态',
    remark TEXT COMMENT '备注',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (asset_id) REFERENCES asset_basic_info(id) ON DELETE CASCADE,
    INDEX idx_asset_id (asset_id),
    INDEX idx_rental_dates (rental_start_date, rental_end_date),
    INDEX idx_contract_status (contract_status)
) COMMENT='出租信息表';
```

### 3.4 资产财务信息表 (asset_financial_info)
```sql
CREATE TABLE asset_financial_info (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    asset_id BIGINT NOT NULL COMMENT '资产ID',
    accounting_date DATE NOT NULL COMMENT '入账时间',
    original_value DECIMAL(20,2) NOT NULL COMMENT '入账原值',
    depreciation_years INT NOT NULL COMMENT '折旧年限',
    depreciation_method ENUM('STRAIGHT_LINE', 'DECLINING_BALANCE', 'SUM_OF_YEARS') DEFAULT 'STRAIGHT_LINE' COMMENT '折旧方法',
    residual_rate DECIMAL(5,4) DEFAULT 0.05 COMMENT '残值率',
    book_value DECIMAL(20,2) NOT NULL COMMENT '账面价值',
    financial_year INT NOT NULL COMMENT '财务年度',
    financial_month INT NOT NULL COMMENT '财务月份',
    accumulated_depreciation DECIMAL(20,2) DEFAULT 0 COMMENT '累计折旧',
    monthly_depreciation DECIMAL(20,2) DEFAULT 0 COMMENT '月折旧额',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (asset_id) REFERENCES asset_basic_info(id) ON DELETE CASCADE,
    INDEX idx_asset_id (asset_id),
    INDEX idx_financial_period (financial_year, financial_month),
    UNIQUE KEY uk_asset_financial_period (asset_id, financial_year, financial_month)
) COMMENT='资产财务信息表';
```

### 3.5 文件附件表 (asset_attachments)
```sql
CREATE TABLE asset_attachments (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    asset_id BIGINT NOT NULL COMMENT '资产ID',
    file_name VARCHAR(255) NOT NULL COMMENT '文件名',
    file_path VARCHAR(500) NOT NULL COMMENT '文件路径',
    file_size BIGINT COMMENT '文件大小(字节)',
    file_type VARCHAR(50) COMMENT '文件类型',
    attachment_type ENUM('PURCHASE_CONTRACT', 'RENTAL_CONTRACT', 'PROPERTY_CERTIFICATE', 'OTHER') NOT NULL COMMENT '附件类型',
    upload_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '上传时间',
    uploaded_by VARCHAR(50) COMMENT '上传人',
    sync_status ENUM('PENDING', 'SYNCED', 'FAILED') DEFAULT 'PENDING' COMMENT '同步状态',
    sync_time DATETIME COMMENT '同步时间',
    FOREIGN KEY (asset_id) REFERENCES asset_basic_info(id) ON DELETE CASCADE,
    INDEX idx_asset_id (asset_id),
    INDEX idx_attachment_type (attachment_type),
    INDEX idx_sync_status (sync_status)
) COMMENT='文件附件表';
```

## 4. API接口设计

### 4.1 资产基本信息接口
- GET /api/assets - 获取资产列表
- GET /api/assets/{id} - 获取资产详情
- POST /api/assets - 创建资产
- PUT /api/assets/{id} - 更新资产
- DELETE /api/assets/{id} - 删除资产

### 4.2 资产状态接口
- GET /api/assets/{id}/status - 获取资产状态
- POST /api/assets/{id}/status - 更新资产状态
- GET /api/assets/{id}/rental-info - 获取出租信息
- POST /api/assets/{id}/rental-info - 添加出租信息

### 4.3 资产财务信息接口
- GET /api/assets/{id}/financial - 获取财务信息
- POST /api/assets/{id}/financial - 添加财务信息
- GET /api/assets/financial/summary - 获取财务汇总

### 4.4 文件管理接口
- POST /api/assets/{id}/attachments - 上传文件
- GET /api/assets/{id}/attachments - 获取文件列表
- DELETE /api/attachments/{id} - 删除文件
- GET /api/attachments/{id}/download - 下载文件

### 4.5 数据统计接口
- GET /api/assets/statistics/area-distribution - 面积分布统计
- GET /api/assets/statistics/company-activation - 公司盘活统计
- GET /api/assets/statistics/defective-assets - 瑕疵资产统计

## 5. 文件存储和同步方案

### 5.1 文件存储结构
```
/var/financial-system/assets/
├── contracts/          # 合同文件
├── certificates/       # 证书文件
├── images/            # 图片文件
└── others/            # 其他文件
```

### 5.2 同步机制
1. **实时同步**：使用rsync + inotify实现文件变更监控
2. **定时同步**：每日定时全量同步
3. **双向同步**：支持Linux服务器与Mac的双向文件同步
4. **冲突处理**：基于时间戳的冲突解决策略

## 6. 前端组件架构

### 6.1 页面结构
```
AssetManagement/
├── AssetBasicInfo/     # 资产基本信息
├── AssetStatus/        # 资产状态
├── AssetFinancial/     # 资产财务信息
├── AssetCharts/        # 图表组件
└── FileManagement/     # 文件管理
```

### 6.2 图表组件
- PieChart: 使用react-chartjs-2实现面积分布饼图
- BarChart: 使用react-chartjs-2实现公司盘活情况柱状图
- StatisticsCard: 瑕疵资产统计卡片

## 7. 技术实现要点

### 7.1 后端技术栈
- Spring Boot 3.x
- JPA/Hibernate
- MySQL 8.0
- 文件上传：MultipartFile
- 文件同步：rsync + shell脚本

### 7.2 前端技术栈
- React 18
- Material-UI
- Chart.js/React-chartjs-2
- Axios (HTTP客户端)

### 7.3 部署和运维
- Docker容器化部署
- 文件同步脚本自动化
- 数据库备份策略
