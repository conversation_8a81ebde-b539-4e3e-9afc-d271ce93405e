###
# 债权转换API测试文件
# 使用 REST Client 或 Postman 进行测试
###

### 环境变量
@baseUrl = http://localhost:8080
@jwt_token = your_jwt_token_here

### 1. 搜索可转换债权（无参数 - 获取所有记录）
GET {{baseUrl}}/api/debts/conversion/search
Authorization: Bearer {{jwt_token}}
Content-Type: application/json

### 2. 搜索可转换债权（按债权人搜索）
GET {{baseUrl}}/api/debts/conversion/search?creditor=中国银行
Authorization: Bearer {{jwt_token}}
Content-Type: application/json

### 3. 搜索可转换债权（按债务人搜索）
GET {{baseUrl}}/api/debts/conversion/search?debtor=某某公司
Authorization: Bearer {{jwt_token}}
Content-Type: application/json

### 4. 搜索可转换债权（按债权人和债务人搜索）
GET {{baseUrl}}/api/debts/conversion/search?creditor=中国银行&debtor=某某公司
Authorization: Bearer {{jwt_token}}
Content-Type: application/json

### 5. 诉讼转非诉讼
POST {{baseUrl}}/api/debts/conversion/litigation-to-non-litigation
Authorization: Bearer {{jwt_token}}
Content-Type: application/json

{
    "creditor": "中国银行",
    "debtor": "某某公司",
    "period": "2025年新增债权",
    "year": 2025,
    "month": 1,
    "conversionYear": 2025,
    "conversionMonth": 7,
    "remark": "API测试 - 诉讼转非诉讼"
}

### 6. 非诉讼转诉讼
POST {{baseUrl}}/api/debts/conversion/non-litigation-to-litigation
Authorization: Bearer {{jwt_token}}
Content-Type: application/json

{
    "creditor": "中国银行",
    "debtor": "某某公司",
    "period": "2025年新增债权",
    "year": 2025,
    "month": 1,
    "conversionYear": 2025,
    "conversionMonth": 7,
    "litigationCase": "某某公司债权纠纷案",
    "litigationOccurredPrincipal": 1000000.00,
    "litigationInterestFee": 50000.00,
    "litigationFee": 15000.00,
    "intermediaryFee": 10000.00,
    "remark": "API测试 - 非诉讼转诉讼"
}

### 7. 非诉讼转诉讼（缺少必填字段测试）
POST {{baseUrl}}/api/debts/conversion/non-litigation-to-litigation
Authorization: Bearer {{jwt_token}}
Content-Type: application/json

{
    "creditor": "中国银行",
    "debtor": "某某公司",
    "period": "2025年新增债权",
    "year": 2025,
    "month": 1,
    "conversionYear": 2025,
    "conversionMonth": 7,
    "remark": "缺少诉讼案件名称的测试"
}

### 8. 错误测试 - 缺少债权人
POST {{baseUrl}}/api/debts/conversion/litigation-to-non-litigation
Authorization: Bearer {{jwt_token}}
Content-Type: application/json

{
    "debtor": "某某公司",
    "period": "2025年新增债权",
    "year": 2025,
    "month": 1,
    "conversionYear": 2025,
    "conversionMonth": 7,
    "remark": "缺少债权人的测试"
}

### 9. 错误测试 - 无效年份
POST {{baseUrl}}/api/debts/conversion/litigation-to-non-litigation
Authorization: Bearer {{jwt_token}}
Content-Type: application/json

{
    "creditor": "中国银行",
    "debtor": "某某公司",
    "period": "2025年新增债权",
    "year": 1999,
    "month": 1,
    "conversionYear": 2025,
    "conversionMonth": 7,
    "remark": "无效年份测试"
}