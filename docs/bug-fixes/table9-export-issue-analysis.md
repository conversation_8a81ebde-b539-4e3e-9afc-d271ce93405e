# 表9导出无数据问题分析

## 问题描述
表9（新增逾期债权明细表）导出时无数据，需要深入分析原因。

## 问题分析

### 1. SQL查询逻辑分析

在`ExcelExportOverdueDebt.java`的`buildQueryAndParams`方法中，表9的SQL查询逻辑如下：

```sql
-- 第696-771行的查询逻辑
WITH new_debt_data AS (
    SELECT
        债权人,
        债务人,
        是否涉诉,
        期间,
        科目名称,
        债权性质,
        IFNULL(`1月`, 0) AS `1月`,
        IFNULL(`2月`, 0) AS `2月`,
        -- ... 其他月份
        备注
    FROM 新增表
    WHERE 年份 = ?
),
disposal_data AS (
    SELECT
        债权人,
        债务人,
        是否涉诉,
        期间,
        SUM(现金处置 + 分期还款 + 资产抵债 + 其他方式) AS 累计处置金额
    FROM 处置表
    WHERE 年份 = ? AND 月份 <= ?
    GROUP BY 债权人, 债务人, 是否涉诉, 期间
)
SELECT
    nd.债权人,
    nd.债务人,
    -- ... 其他字段
FROM new_debt_data nd
LEFT JOIN disposal_data dd ON
    nd.债权人 = dd.债权人 AND
    nd.债务人 = dd.债务人 AND
    nd.是否涉诉 = dd.是否涉诉 AND
    nd.期间 = dd.期间
WHERE
    (nd.`1月` <> 0 OR nd.`2月` <> 0 OR ... OR nd.`12月` <> 0)
ORDER BY nd.债权人, nd.债务人, nd.是否涉诉
```

### 2. 逻辑冲突分析

**核心问题：IFNULL处理与WHERE条件的逻辑冲突**

1. **CTE中的IFNULL处理**：
   - 在`new_debt_data` CTE中，对所有月份字段使用了`IFNULL(字段, 0)`
   - 这会将所有NULL值转换为0

2. **WHERE条件检查**：
   - WHERE条件使用`nd.`前缀引用CTE的字段
   - 但是CTE中已经将NULL转为0，所以WHERE条件实际上是检查处理后的值
   - 如果原始数据中月份字段都是NULL或0，经过IFNULL处理后都变成0，WHERE条件就会过滤掉所有记录

3. **动态月份列生成**：
   - 第700-718行动态生成月份列和WHERE条件
   - WHERE条件检查所有12个月，但CTE中只选择了指定月份（1到month）
   - 这导致未选择的月份在WHERE条件中会被当作NULL处理

### 3. 具体问题场景

假设新增表中有一条记录：
- 年份 = 2025
- 1月 = NULL
- 2月 = NULL
- 3月 = 10.00
- ... (其他月份为NULL)

执行流程：
1. CTE选择1月和2月（month=2），并使用IFNULL转换：
   - 1月 = 0
   - 2月 = 0
   - 3月未被选择（不在SELECT列表中）

2. WHERE条件检查所有12个月：
   - nd.`1月` <> 0 → FALSE (0 <> 0)
   - nd.`2月` <> 0 → FALSE (0 <> 0)
   - nd.`3月` <> 0 → 由于3月不在SELECT中，值为NULL，NULL <> 0 → NULL → FALSE
   - ...
   - 整个WHERE条件结果为FALSE，记录被过滤

## 解决方案

### 方案1：修正WHERE条件逻辑（推荐）

修改WHERE条件，直接查询原始表而不是CTE：

```java
// 修改buildQueryAndParams方法中的表9查询
sql = String.format("""
    WITH new_debt_data AS (
        SELECT
            债权人,
            债务人,
            是否涉诉,
            期间,
            科目名称,
            债权性质,
            %s,
            备注
        FROM 新增表
        WHERE 年份 = ?
          AND (%s)  -- 在CTE中就进行过滤
    ),
    disposal_data AS (
        -- 保持不变
    )
    SELECT
        nd.债权人,
        nd.债务人,
        -- ... 其他字段
    FROM new_debt_data nd
    LEFT JOIN disposal_data dd ON
        -- 保持不变
    ORDER BY nd.债权人, nd.债务人, nd.是否涉诉
    """, monthColumns.toString(), monthWhereConditions.toString());
```

### 方案2：选择所有月份字段

修改月份列生成逻辑，始终选择所有12个月：

```java
// 修改第700-705行
StringBuilder monthColumns = new StringBuilder();
for (int i = 1; i <= 12; i++) {  // 改为12而不是month
    String monthCol = i + "月";
    monthColumns.append("IFNULL(`").append(monthCol).append("`, 0) AS `").append(monthCol).append("`, ");
}
```

### 方案3：修改WHERE条件引用

在WHERE条件中直接引用原始表字段：

```java
// 修改第706-713行的WHERE条件生成
for (int i = 1; i <= 12; i++) {
    String monthCol = i + "月";
    if (monthWhereConditions.length() > 0) {
        monthWhereConditions.append(" OR ");
    }
    // 使用子查询检查原始表
    monthWhereConditions.append("EXISTS (SELECT 1 FROM 新增表 n WHERE n.债权人 = nd.债权人 AND n.债务人 = nd.债务人 AND n.`").append(monthCol).append("` <> 0)");
}
```

## 建议测试步骤

1. 先运行测试SQL脚本，确认新增表中的数据情况
2. 根据实际数据情况选择合适的解决方案
3. 修改代码后进行充分测试，确保：
   - 有数据的情况能正确导出
   - 处置金额能正确关联
   - 性能不受太大影响

## 最佳实践建议

1. **避免在CTE中使用IFNULL后再在外层WHERE中引用**
2. **WHERE条件应该基于原始数据，而不是处理后的数据**
3. **动态生成SQL时，确保SELECT列表和WHERE条件的一致性**
4. **添加详细的日志记录，便于调试SQL问题**