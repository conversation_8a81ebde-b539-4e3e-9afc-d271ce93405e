# 表9导出SQL回退方案

## 回退到原始SQL

如果需要回退到原始的SQL查询逻辑，以下是最简单的修改方案：

### 修改文件：`ExcelExportOverdueDebt.java`

在`buildQueryAndParams`方法中，将表9的查询改回最简单的形式：

```java
} else if ("表9-新增逾期债权明细表".equals(tableName)) {
    // 使用最简单直接的查询方式
    sql = """
        SELECT 
            债权人,
            债务人,
            是否涉诉,
            期间,
            科目名称,
            债权性质,
            `1月`, `2月`, `3月`, `4月`, `5月`, `6月`,
            `7月`, `8月`, `9月`, `10月`, `11月`, `12月`,
            新增金额,
            备注
        FROM 新增表
        WHERE 年份 = ?
        ORDER BY 债权人, 债务人, 是否涉诉
        """;
    
    // 添加参数
    params.add(year);  // 新增表年份
}
```

## 如果需要保留处置金额关联

如果业务需要显示处置金额，可以使用这个版本：

```java
} else if ("表9-新增逾期债权明细表".equals(tableName)) {
    // 简单的左连接查询
    sql = """
        SELECT 
            n.债权人,
            n.债务人,
            n.是否涉诉,
            n.期间,
            n.科目名称,
            n.债权性质,
            n.`1月`, n.`2月`, n.`3月`, n.`4月`, n.`5月`, n.`6月`,
            n.`7月`, n.`8月`, n.`9月`, n.`10月`, n.`11月`, n.`12月`,
            n.新增金额,
            IFNULL(d.累计处置金额, 0) AS 累计处置金额,
            n.备注
        FROM 新增表 n
        LEFT JOIN (
            SELECT 
                债权人, 债务人, 是否涉诉, 期间,
                SUM(现金处置 + 分期还款 + 资产抵债 + 其他方式) AS 累计处置金额
            FROM 处置表
            WHERE 年份 = ? AND 月份 <= ?
            GROUP BY 债权人, 债务人, 是否涉诉, 期间
        ) d ON n.债权人 = d.债权人 
           AND n.债务人 = d.债务人 
           AND n.是否涉诉 = d.是否涉诉 
           AND n.期间 = d.期间
        WHERE n.年份 = ?
        ORDER BY n.债权人, n.债务人, n.是否涉诉
        """;
    
    // 添加参数
    params.add(year);   // 处置表年份
    params.add(month);  // 处置表月份上限
    params.add(year);   // 新增表年份
}
```

## 完整的代码修改

找到`ExcelExportOverdueDebt.java`文件的第696-771行，替换为上述代码即可。

### 原始代码位置
```java
// 第696行开始
} else if ("表9-新增逾期债权明细表".equals(tableName)) {
    // ... 原有的复杂查询逻辑
}
```

### 替换后的代码
选择上面提供的两个版本之一进行替换。

## 注意事项

1. **这种方式会导出所有2025年的数据**，不管月份字段是否有值
2. **如果需要过滤零值记录**，可以在Excel中手动过滤或使用Excel公式
3. **确保Excel模板中的列映射与查询结果匹配**

## 验证SQL

使用以下SQL验证数据：

```sql
-- 查看将要导出的数据量
SELECT COUNT(*) FROM 新增表 WHERE 年份 = '2025';

-- 查看前10条数据
SELECT * FROM 新增表 WHERE 年份 = '2025' LIMIT 10;
```

这是最简单的回退方案，能确保数据能够导出，具体的过滤和处理可以在Excel端完成。