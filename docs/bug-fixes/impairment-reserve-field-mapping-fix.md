# 减值准备表字段映射错误修复方案

## 问题描述

在 `/api/debts/decreases/search` API 返回的数据中，减值准备余额字段映射错误：
- 返回的是债权余额而不是减值准备余额
- 导致前端显示的减值准备数据错误

## 问题原因

在 `OverdueDebtDecreaseService.findDebtorInfoByCreditorAndDebtor` 方法中（第899-939行），字段映射存在错误：

```java
// 当前错误的映射
recordMap.put("lastMonthBalance", record.getLastMonthBalance());        // 这是债权余额
recordMap.put("currentMonthBalance", record.getCurrentMonthBalance());  // 这是债权余额
```

## 字段说明

减值准备表（ImpairmentReserve）包含两套余额字段：

### 1. 债权余额字段
- `lastMonthBalance` - 本月初债权余额
- `currentMonthBalance` - 本月末债权余额

### 2. 减值准备余额字段
- `previousMonthBalance` - 上月末余额（减值准备）
- `currentMonthAmount` - 本月末余额（减值准备）

## 修复方案

### 方案一：修改后端字段映射（推荐）

修改 `OverdueDebtDecreaseService.java` 第928-932行：

```java
// 添加金额相关字段
recordMap.put("lastMonthBalance", record.getLastMonthBalance());               // 保留债权余额
recordMap.put("currentMonthBalance", record.getCurrentMonthBalance());         // 保留债权余额
recordMap.put("previousMonthBalance", record.getPreviousMonthBalance());       // 添加减值准备上月末余额
recordMap.put("currentMonthAmount", record.getCurrentMonthAmount());           // 添加减值准备本月末余额
recordMap.put("currentMonthNewDebt", record.getCurrentMonthNewDebt());
recordMap.put("currentMonthDisposeDebt", record.getCurrentMonthDisposeDebt());
```

### 方案二：仅修改必要字段（最小改动）

如果前端只需要减值准备余额，可以直接替换：

```java
// 添加金额相关字段
recordMap.put("previousMonthBalance", record.getPreviousMonthBalance());       // 改为减值准备上月末余额
recordMap.put("currentMonthNewDebt", record.getCurrentMonthNewDebt());
recordMap.put("currentMonthDisposeDebt", record.getCurrentMonthDisposeDebt());
recordMap.put("currentMonthBalance", record.getCurrentMonthAmount());          // 改为减值准备本月末余额
```

## 前端适配

前端需要根据后端返回的字段调整：

```javascript
// 如果采用方案一，前端使用新字段
const previousBalance = record.previousMonthBalance;  // 减值准备上月末余额
const currentBalance = record.currentMonthAmount;     // 减值准备本月末余额

// 如果采用方案二，前端代码无需修改
const previousBalance = record.previousMonthBalance;
const currentBalance = record.currentMonthBalance;
```

## 影响范围

1. `/api/debts/decreases/search` API
2. 前端债务处置页面的减值准备显示
3. 可能影响其他使用该API的功能

## 测试要点

1. 验证返回的减值准备余额是否正确
2. 确认债权余额和减值准备余额的区别
3. 测试前端显示是否正确
4. 回归测试其他相关功能

## 注意事项

1. 减值准备余额和债权余额是两个不同的概念
2. 修改后需要通知前端开发人员更新字段使用
3. 建议在API文档中明确说明各字段的含义