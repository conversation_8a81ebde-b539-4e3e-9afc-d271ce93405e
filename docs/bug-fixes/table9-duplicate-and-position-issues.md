# 表9导出数据重复和小计位置错误问题分析

## 问题描述

表9（新增逾期债权明细表）导出存在两个问题：
1. 数据重复：第3行和第4行数据完全相同
2. 存量新增小计位置错误：小计行上面全是"2025年新增债权"，不符合业务逻辑

## 问题根源分析

### 1. 数据重复的根本原因

#### SQL查询逻辑问题
在`ExcelExportOverdueDebt.java`第756-759行：
```sql
CASE
  WHEN IFNULL(ysb.年初余额, 0) = 0 THEN '2025年新增债权'
  ELSE '存量新增债权'
END AS 期间
```

**问题分析**：
- SQL查询覆盖了原始的期间字段值
- 新增表中可能有多条记录，只是期间字段不同
- 但CASE语句将所有年初余额为0的记录统一标记为"2025年新增债权"
- 导致原本不同的记录在显示时看起来相同

#### JOIN条件不匹配
```sql
LEFT JOIN disposal_data dd ON
    nd.债权人 = dd.债权人 AND
    nd.债务人 = dd.债务人 AND
    nd.是否涉诉 = dd.是否涉诉 AND
    nd.期间 = dd.期间  -- 这里使用的是原始期间值
```
- disposal_data的JOIN使用`nd.期间`（原始值）
- 但最终显示的期间是CASE计算后的值
- 这种不一致可能导致JOIN失败或产生意外结果

### 2. 存量新增小计位置错误

#### Excel模板结构
- 模板中预设了"存量新增小计"行作为分隔线
- 设计意图：存量债权在上，新增债权在下

#### 数据插入逻辑（第934-943行）
```java
if ("表9-新增逾期债权明细表".equals(tableName)) {
    switch (periodValue) {
        case "存量新增债权":
            break;  // 从默认位置（第4行）开始
        case "2025年新增债权":
            startRow = findRowByTextOrThrow(cells, "存量新增小计", searchColumn) + 1;
            break;
    }
}
```

#### 期间判断逻辑错误
```sql
WHEN IFNULL(ysb.年初余额, 0) = 0 THEN '2025年新增债权'
ELSE '存量新增债权'
```
**问题**：
- 逻辑错误：基于年初余额是否为0来判断存量vs新增
- 正确逻辑：应基于债权发生时间
  - 2025年之前发生 = 存量债权
  - 2025年新发生 = 新增债权
- 当前所有年初余额为0的记录都被标记为"2025年新增债权"
- 导致所有数据都插入到小计行之后

## 修复方案

### 方案1：保留原始期间字段（推荐）

修改SQL查询，不覆盖期间字段：

```java
sql = String.format("""
    WITH new_debt_data AS (
        SELECT
            债权人,
            债务人,
            是否涉诉,
            期间,  -- 保留原始期间值
            科目名称,
            债权性质,
            %s,
            备注
        FROM 新增表
        WHERE 年份 = ?
    ),
    -- ... 其他CTE保持不变
    SELECT DISTINCT  -- 添加DISTINCT去重
        nd.债权人,
        nd.债务人,
        nd.是否涉诉,
        nd.期间,  -- 使用原始期间值
        IFNULL(ysb.年初余额, 0) AS 年初余额,
        -- ... 其他字段
    FROM new_debt_data nd
    -- ... JOIN条件保持不变
    ORDER BY 
        CASE 
            WHEN nd.期间 LIKE '%%存量%%' THEN 0
            ELSE 1
        END,
        nd.债权人, nd.债务人, nd.是否涉诉
    """, monthColumns.toString());
```

### 方案2：修正期间判断逻辑

如果必须动态生成期间，应基于更合理的逻辑：

```sql
CASE
  -- 基于期间字段本身的值来判断
  WHEN nd.期间 LIKE '%存量%' OR nd.期间 < '2025年' THEN '存量新增债权'
  WHEN nd.期间 = '2025年新增债权' OR nd.期间 >= '2025年' THEN '2025年新增债权'
  ELSE nd.期间  -- 保留原值
END AS 期间
```

### 方案3：修改数据插入逻辑

调整`findTargetRowByPeriod`方法，支持更多期间类型：

```java
case "存量新增债权":
case "2024年新增债权":  // 2024年的也算存量
case "2023年新增债权":  // 2023年的也算存量
    break;  // 插入到小计行之前
case "2025年新增债权":
    startRow = findRowByTextOrThrow(cells, "存量新增小计", searchColumn) + 1;
    break;
```

## 建议采用的解决方案

**推荐方案1**，因为：
1. 保留原始数据的完整性
2. 避免覆盖期间字段导致的逻辑混乱
3. 通过ORDER BY确保正确的数据排序
4. 使用DISTINCT解决潜在的重复问题

## 验证步骤

1. 检查新增表的原始数据：
```sql
SELECT 债权人, 债务人, 是否涉诉, 期间, COUNT(*) as cnt
FROM 新增表
WHERE 年份 = '2025'
GROUP BY 债权人, 债务人, 是否涉诉, 期间
HAVING cnt > 1;
```

2. 验证年初余额数据：
```sql
SELECT COUNT(*) 
FROM 减值准备表
WHERE 年份 = '2024' AND 月份 = 12 AND 本月末债权余额 <> 0;
```

3. 修改后重新导出，检查：
   - 是否还有重复数据
   - 存量债权是否正确显示在小计行之前
   - 新增债权是否正确显示在小计行之后