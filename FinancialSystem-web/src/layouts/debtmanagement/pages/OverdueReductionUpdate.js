import React, { useEffect, useState } from 'react';
import {
  <PERSON>ert,
  Box,
  Button,
  Card,
  CardContent,
  CircularProgress,
  Container,
  Grid,
  Paper,
  TextField,
  Typography
} from '@mui/material';
import GenericDataTable from 'components/tables/GenericDataTable';
import DashboardLayout from 'examples/LayoutContainers/DashboardLayout';
import DashboardNavbar from 'examples/Navbars/DashboardNavbar';
import FormInput from '../components/inputform/FormInput';
import FormSelect from '../components/inputform/FormSelect';
import { FormMonthPicker } from '../../../components/forms';
import MDButton from 'components/MDButton';
import api from '../../../utils/api';
import { useAuth } from '../../../context/AuthContext';

const OverdueReductionUpdate = () => {
  const { user } = useAuth(); // 获取当前用户信息
  const [debtor, setDebtor] = useState('');
  const [creditor, setCreditor] = useState(''); // 债权人
  const [managementCompany, setManagementCompany] = useState(
    user?.role === 'ADMIN' ? '' : user?.company || ''
  ); // 管理公司
  const [debtPeriod, setDebtPeriod] = useState(''); // 债权归属期间
  const [debtDetails, setDebtDetails] = useState(null); // 用来存储债务人的信息
  const [dispositionAmount, setDispositionAmount] = useState(''); // 使用空字符串作为初始值
  const [dispositionDetails, setDispositionDetails] = useState({
    cashAmount: '', // 使用空字符串作为初始值
    assetAmount: '',
    inventoryAmount: '',
    adjustmentAmount: '',
    otherAmount: ''
  });
  const [yearMonth, setYearMonth] = useState('');
  const [debtAmount, setDebtAmount] = useState(''); // 债权金额
  const [caseName, setCaseName] = useState(''); // 案件名称
  const [isLitigation, setIsLitigation] = useState('否'); // 是否涉诉，默认为否
  const [remark, setRemark] = useState(''); // 备注
  const [error, setError] = useState('');
  const [amountError, setAmountError] = useState('');
  const [isSearching, setIsSearching] = useState(false); // 是否正在搜索中
  // 添加输入警告状态
  const [inputWarnings, setInputWarnings] = useState({});

  // 自动搜索功能已实现，不需要预设选项

  // 处置方式标签映射
  const dispositionTypeLabels = {
    cashAmount: '现金',
    assetAmount: '分期还款',
    inventoryAmount: '资产抵债',
    adjustmentAmount: '账务调整',
    otherAmount: '其他'
  };

  useEffect(() => {
    // 获取当前年份和月份
    const date = new Date();
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    setYearMonth(`${year}-${month}`);
  }, []);

  // 计算所有处置方式金额的总和
  const calculateTotalDispositionAmount = () => {
    // 确保每个值都被转换为数字
    const total = Object.values(dispositionDetails).reduce((sum, amount) => {
      // 空字符串视为0
      const numAmount = amount === '' ? 0 : parseFloat(amount);
      const validAmount = isNaN(numAmount) ? 0 : numAmount;
      return sum + validAmount;
    }, 0);

    return total;
  };

  // 验证处置金额总和是否等于新增处置金额
  const validateAmounts = () => {
    // 检查新增处置金额是否已填写
    if (!dispositionAmount || dispositionAmount.trim() === '') {
      setError('请填写新增处置金额');
      return false;
    }

    // 检查金额是否为负数
    if (parseFloat(dispositionAmount) < 0) {
      setAmountError('不能为负数');

      // 当输入负数时，更新错误消息显示实际输入的负数
      const total = calculateTotalDispositionAmount();
      const displayTotal = total.toFixed(2);
      const displayDispositionAmount = parseFloat(dispositionAmount).toFixed(2);
      const errorMsg = `处置金额合计(${displayTotal}万元)与新增处置金额(${displayDispositionAmount}万元)不一致`;
      setError(errorMsg);
      return false;
    }
    const total = calculateTotalDispositionAmount();

    // 修复浮点数精度问题：使用舍入并允许0.001的误差
    const roundedTotal = Math.round(total * 1000) / 1000;

    // 空字符串视为0
    const dispositionAmountValue = dispositionAmount === '' ? 0 : parseFloat(dispositionAmount);

    const roundedDispositionAmount = Math.round(dispositionAmountValue * 1000) / 1000;

    const isValid = Math.abs(roundedTotal - roundedDispositionAmount) < 0.001;

    if (!isValid) {
      // 保留两位小数以便于阅读
      const displayTotal = total.toFixed(2);
      const displayDispositionAmount = dispositionAmountValue.toFixed(2);
      const errorMsg = `处置金额合计(${displayTotal}万元)与新增处置金额(${displayDispositionAmount}万元)不一致`;

      setError(errorMsg);
    } else {
      setError('');
    }

    return isValid;
  };

  // 格式化货币显示
  const formatCurrency = amount => {
    if (amount === null || amount === undefined) {
      return '-';
    }
    return new Intl.NumberFormat('zh-CN', {
      style: 'currency',
      currency: 'CNY',
      minimumFractionDigits: 2
    }).format(amount);
  };

  // 添加useEffect来监听输入变化并更新验证
  useEffect(() => {
    // 当金额输入变化时验证
    validateAmounts();
  }, [dispositionAmount, dispositionDetails, validateAmounts]);

  // 自动搜索功能 - 严格复制OverdueDebtAdd中的autoSearch实现
  const [searchResults, setSearchResults] = useState([]);
  const [hasSearched, setHasSearched] = useState(false);
  const [selectedRows, setSelectedRows] = useState({});

  // 债权处置记录数据状态
  const [disposalRecords, setDisposalRecords] = useState([]);
  const [isLoadingDisposalRecords, setIsLoadingDisposalRecords] = useState(false);
  const [deletingRecordIds, setDeletingRecordIds] = useState({}); // 使用对象跟踪正在删除的记录ID

  // 获取上表格所需的债务记录 - 仅用于自动搜索
  const fetchDisposalRecords = async (searchCreditor, searchDebtor) => {
    if (!searchCreditor && !searchDebtor) {
      return;
    }

    try {
      // 构建查询参数
      const queryParams = new URLSearchParams();
      if (searchCreditor) {
        queryParams.append('creditor', searchCreditor);
      }
      if (searchDebtor) {
        queryParams.append('debtor', searchDebtor);
      }

      // 调用上表格的数据搜索API
      const response = await api.get(`/debts/decreases/search?${queryParams.toString()}`);

      if (response.status === 200) {
        // 处理上表格的数据
      }
    } catch (error) {
      console.error('自动搜索错误:', error);
    }
  };

  // 获取下表格的当前年度债权处置记录
  // 处理删除债权处置记录的函数
  const handleDeleteRecord = async record => {
    // 检查是否已经在处理中，防止重复点击
    if (deletingRecordIds[record.id]) {
      return;
    }

    if (
      !window.confirm(
        `确定要删除该处置记录吗？\n\n债权人: ${record.creditor}\n债务人: ${record.debtor}\n月份: ${
          record.month
        }\n处置金额: ${formatCurrency(record.disposalAmount).replace('¥', '')}元`
      )
    ) {
      return; // 用户取消删除
    }

    // 将当前记录标记为正在删除状态
    setDeletingRecordIds(prev => ({ ...prev, [record.id]: true }));

    try {
      // 获取当前年份
      const currentYear = new Date().getFullYear();

      // 格式化月份，确保是两位数
      let month = record.month;
      // 如果month是数字，转为字符串
      if (typeof month === 'number') {
        month = month.toString();
      }

      // 移除可能存在的年份部分，只保留月份数字
      const monthPart = month.includes('-') ? month.split('-')[1] : month;

      // 格式化为两位数月份，转换为数字
      const monthNumber = parseInt(monthPart);

      // 检查原始数据中的period值
      const originalPeriod = record.period || record.originalData?.period || '';
      console.log('原始period值:', originalPeriod);
      console.log('record对象:', record);
      console.log('originalData对象:', record.originalData);

      // 如果原始period不符合格式，构建新的period
      let finalPeriod = originalPeriod;

      // 处理期间格式，统一规范
      // 1. 如果是 "YYYY年430" 格式，改为 "YYYY年430债权"
      if (originalPeriod && originalPeriod.match(/^\d{4}年430$/)) {
        finalPeriod = originalPeriod + '债权';
        console.log('规范化430格式，从', originalPeriod, '改为', finalPeriod);
      } else if (
        originalPeriod &&
        (originalPeriod.match(/^\d{4}年430债权$/) || originalPeriod.match(/^\d{4}年新增债权$/))
      ) {
        finalPeriod = originalPeriod;
        console.log('期间格式正确，保持不变:', finalPeriod);
      } else {
        // 3. 处理其他不规范格式
        const yearMatch = originalPeriod.match(/(\d{4})年/);
        if (yearMatch) {
          // 如果包含"新增债权"
          if (originalPeriod.includes('新增债权')) {
            finalPeriod = `${yearMatch[1]}年新增债权`;
          } else if (originalPeriod.includes('430')) {
            // 如果包含"430"
            finalPeriod = `${yearMatch[1]}年430债权`;
          } else {
            // 否则使用月份格式
            finalPeriod = `${currentYear}年${monthNumber}月`;
          }
        } else {
          // 如果完全无法解析，使用当前年份和月份
          finalPeriod = `${currentYear}年${monthNumber}月`;
        }
        console.log('处理不规范格式，从', originalPeriod, '改为', finalPeriod);
      }

      // 准备删除数据
      const deleteData = {
        creditor: record.creditor,
        debtor: record.debtor,
        managementCompany: record.managementCompany || '',
        isLitigation: record.isLitigation,
        period: finalPeriod,
        year: currentYear,
        month: monthNumber,
        amount: parseFloat(record.disposalAmount),
        deleteReason: '用户手动删除处置债权记录',
        operatorName: '当前用户' // 实际应该从用户上下文获取
      };

      console.log('发送的删除数据:', deleteData);

      // 调用新的删除API接口
      const response = await api.post('/debt/deletion/disposal', deleteData, {
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (response.status === 200 && response.data.success) {
        alert(response.data.message || '删除成功');
        // 重新获取处置记录数据
        await fetchCurrentYearDisposalRecords();
      } else {
        alert(`删除失败: ${response.data?.message || response.statusText}`);
      }
    } catch (error) {
      console.error('删除过程中出错:', error);
      alert(`删除出错: ${error.message || '未知错误'}`);
    } finally {
      // 删除操作完成，取消该记录的删除状态
      setDeletingRecordIds(prev => {
        const newState = { ...prev };
        delete newState[record.id];
        return newState;
      });
    }
  };

  const fetchCurrentYearDisposalRecords = async () => {
    setIsLoadingDisposalRecords(true);

    try {
      // 仅获取当年所有债权处置记录
      const url = '/debts/decreases/search-all';

      const response = await api.get(url);

      if (response.status === 200) {
        const records = response.data || [];

        // 处理数据，确保所有字段格式正确
        const processedRecords = records.map((record, index) => {
          return {
            id: index + 1,
            creditor: record.creditor || '',
            debtor: record.debtor || '',
            managementCompany: record.managementCompany || '',
            isLitigation: record.isLitigation || '否',
            month: record.month || '',
            period: record.period || '', // 保存期间字段
            disposalAmount: record.currentMonthDisposeDebt || '0',
            // 保存原始数据引用
            originalData: record
          };
        });

        setDisposalRecords(processedRecords);
      } else {
        console.error('获取当前年度债权处置记录失败:', response.statusText);
        setDisposalRecords([]);
      }
    } catch (error) {
      console.error('获取当前年度债权处置记录错误:', error);
      setDisposalRecords([]);
    } finally {
      setIsLoadingDisposalRecords(false);
    }
  };

  const autoSearch = async (searchCreditor, searchDebtor) => {
    if (!searchCreditor && !searchDebtor) {
      setDebtDetails(null);
      setSearchResults([]);
      setHasSearched(false);
      return;
    }

    setIsSearching(true);
    setHasSearched(true);

    try {
      const queryParams = new URLSearchParams();
      if (searchCreditor) {
        queryParams.append('creditor', searchCreditor);
      }
      if (searchDebtor) {
        queryParams.append('debtor', searchDebtor);
      }

      const response = await api.get(`/debts/decreases/search?${queryParams.toString()}`);
      if (response.status === 200) {
        // 确保搜索结果中的字段正确
        const processedData = (response.data || []).map(item => ({
          ...item,
          creditor: item.creditor || '',
          debtor: item.debtor || '',
          period: item.period || '',
          isLitigation: item.isLitigation || '',
          managementCompany: item.managementCompany || '',
          previousMonthBalance:
            typeof item.previousMonthBalance === 'number'
              ? item.previousMonthBalance
              : parseFloat(item.previousMonthBalance) || 0,
          currentMonthNewDebt:
            typeof item.currentMonthNewDebt === 'number'
              ? item.currentMonthNewDebt
              : parseFloat(item.currentMonthNewDebt) || 0,
          currentMonthDisposeDebt:
            typeof item.currentMonthDisposeDebt === 'number'
              ? item.currentMonthDisposeDebt
              : parseFloat(item.currentMonthDisposeDebt) || 0,
          currentMonthBalance:
            typeof item.currentMonthBalance === 'number'
              ? item.currentMonthBalance
              : parseFloat(item.currentMonthBalance) || 0,
          remark: item.remark || ''
        }));

        // 调试日志
        setSearchResults(processedData);

        // 如果有数据，设置第一条记录的债务详情
        if (processedData && processedData.length > 0) {
          setDebtDetails(processedData[0]);
          console.log(
            '填充字段成功:',
            processedData[0].creditor,
            processedData[0].managementCompany,
            processedData[0].period
          );

          // 只获取上表格的数据，不影响下表格
          await fetchDisposalRecords(processedData[0].creditor, processedData[0].debtor);
        } else {
          // 如果没有找到匹配的记录，清除表单数据

          // 保留搜索条件，但清除其他字段
          setManagementCompany('');
          setDebtPeriod('');
          setYearMonth('');
          setDebtAmount('');
          setDebtDetails(null);

          // 尝试使用搜索条件直接获取上表格数据，不影响下表格
          await fetchDisposalRecords(searchCreditor, searchDebtor);
        }
      }
    } catch (error) {
      console.error('搜索失败:', error);
      setDebtDetails(null);
      setSearchResults([]);
    } finally {
      setIsSearching(false);
    }
  };

  const handleChangeDispositionAmount = event => {
    const value = event.target.value;

    // 检查是否为空
    if (!value || value.trim() === '') {
      setError('请填写新增处置金额');
      setAmountError('');
    } else if (parseFloat(value) < 0) {
      setAmountError('不能为负数');

      // 当输入负数时，更新错误消息
      const total = calculateTotalDispositionAmount();
      const displayTotal = total.toFixed(2);
      const displayDispositionAmount = parseFloat(value).toFixed(2);
      const errorMsg = `处置金额合计(${displayTotal}万元)与新增处置金额(${displayDispositionAmount}万元)不一致`;
      setError(errorMsg);
    } else {
      setAmountError('');
      // 当输入非负数时，清除错误消息
      if (error && (error.includes('与新增处置金额') || error.includes('请填写新增处置金额'))) {
        setError('');
      }
    }

    setDispositionAmount(value);
  };

  // 预处理数据，保证金额字段为数值
  const processedResults = searchResults.map((item, idx) => ({
    id: idx,
    creditor: item.creditor || '',
    debtor: item.debtor || '',
    period: item.period || '',
    isLitigation: item.isLitigation || '',
    managementCompany: item.managementCompany || '',
    previousMonthBalance:
      typeof item.previousMonthBalance === 'number'
        ? item.previousMonthBalance
        : parseFloat(item.previousMonthBalance) || 0,
    currentMonthNewDebt:
      typeof item.currentMonthNewDebt === 'number'
        ? item.currentMonthNewDebt
        : parseFloat(item.currentMonthNewDebt) || 0,
    currentMonthDisposeDebt:
      typeof item.currentMonthDisposeDebt === 'number'
        ? item.currentMonthDisposeDebt
        : parseFloat(item.currentMonthDisposeDebt) || 0,
    currentMonthBalance:
      typeof item.currentMonthBalance === 'number'
        ? item.currentMonthBalance
        : parseFloat(item.currentMonthBalance) || 0,
    remark: item.remark || '',
    selected: Boolean(selectedRows[idx])
  }));

  // 处理选择框点击
  const handleCheckboxClick = rowId => {
    const newSelectedState = !selectedRows[rowId];
    setSelectedRows(prev => ({
      ...prev,
      [rowId]: newSelectedState
    }));

    // 如果选中了行，填充详细信息
    if (newSelectedState) {
      const selectedRow = processedResults.find(item => item.id === rowId);
      if (selectedRow) {
        // 保存当前详细信息以允许提交时使用

        setDebtDetails(selectedRow);

        // 填充表单数据
        setCreditor(selectedRow.creditor || '');
        setDebtor(selectedRow.debtor || '');
        setManagementCompany(selectedRow.managementCompany || '');
        setDebtPeriod(selectedRow.period || '');
        setDebtAmount(selectedRow.debtAmount || '');

        // 如果有isLitigation字段
        if (selectedRow.isLitigation !== undefined) {
          setIsLitigation(selectedRow.isLitigation);
          // 只有当isLitigation为"是"时才设置caseName
          if (selectedRow.isLitigation === '是' && selectedRow.caseName) {
            setCaseName(selectedRow.caseName);
          } else {
            setCaseName(''); // 如果不是涉诉案件，清空caseName
          }
        }
      }
    } else {
      // 当取消选中行时，清空详细信息
      setDebtDetails(null);
    }
  };

  const handleChangeAmount = (type, value) => {
    // 允许空字符串

    setDispositionDetails(prev => {
      const updated = {
        ...prev,
        [type]: value
      };

      return updated;
    });
    // 不再调用setTimeout验证，由useEffect处理
  };

  // 处理处置时间变更
  const handleYearMonthChange = event => {
    setYearMonth(event.target.value);
  };

  // 注意：我们已经在输入字段的onChange事件中直接调用了handleSearch函数
  // 因此不需要使用useEffect来触发自动搜索

  // 在提交之前将所有空字符串转换为0
  const prepareDataForSubmission = () => {
    // 处理总金额 - 确保四舍五入到两位小数
    const finalDispositionAmount =
      dispositionAmount === '' ? 0 : parseFloat(parseFloat(dispositionAmount).toFixed(2));

    // 如果输入值超过两位小数，输出日志以便调试
    if (dispositionAmount && dispositionAmount.includes('.')) {
      const decimalPart = dispositionAmount.split('.')[1];
      if (decimalPart && decimalPart.length > 2) {
      }
    }

    // 处理各项金额
    const finalDispositionDetails = {};
    Object.keys(dispositionDetails).forEach(key => {
      finalDispositionDetails[key] =
        dispositionDetails[key] === '' ? 0 : parseFloat(dispositionDetails[key]);
    });

    // 直接使用用户在界面上选择的值，不再从查询结果中获取isLitigation值
    // 这与OverdueDebtAdd.js的实现方式保持一致
    const finalIsLitigation = isLitigation || '否';
    let finalPeriod = debtPeriod || '';
    let finalManagementCompany = managementCompany || '';
    let finalDebtPeriod = debtPeriod || '';

    // 如果有筛选结果，获取其他非isLitigation字段信息
    if (debtDetails) {
      // 获取期间信息
      finalPeriod = debtDetails.period !== undefined ? debtDetails.period : finalPeriod;

      // 获取管理公司信息
      finalManagementCompany = debtDetails.managementCompany || finalManagementCompany;

      // 获取债权期间信息
      finalDebtPeriod = debtDetails.period || finalDebtPeriod;
    }

    // 输出当前使用的是否涉诉值

    // 创建提交数据对象
    const submitData = {
      dispositionAmount: finalDispositionAmount,
      dispositionDetails: finalDispositionDetails,
      // 其他数据不变
      debtor,
      creditor,
      managementCompany: finalManagementCompany,
      debtPeriod: finalDebtPeriod,
      period: finalPeriod,
      isLitigation: finalIsLitigation, // 使用用户选择的是否涉诉字段
      yearMonth,
      remark
    };

    // 仅当筛选结果中有caseName且isLitigation为"是"时，才添加caseName字段
    if (debtDetails && debtDetails.caseName && finalIsLitigation === '是') {
      submitData.caseName = debtDetails.caseName;
    } else if (caseName && finalIsLitigation === '是') {
      // 如果用户手动设置了caseName且是涉诉案件，也添加
      submitData.caseName = caseName;
    }

    return submitData;
  };

  const handleSubmit = async event => {
    // 阻止表单默认提交行为
    if (event) {
      event.preventDefault();
    }

    if (!validateAmounts()) {
      return; // 如果验证失败，不提交
    }

    // 准备提交数据
    const dataToSubmit = prepareDataForSubmission();

    // 直接使用界面上的是否涉诉值，不再需要验证它是否来自筛选结果

    // 在这里输出一下完整的数据结构，确认是否涉诉字段存在

    // 为了调试，单独输出该字段

    // 发送到后端API
    try {
      // 使用显式参数确保所有字段都被发送
      const response = await api.post('/debts/update/reduction', dataToSubmit, {
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (response.status === 200) {
        alert('提交成功');

        // 保存当前的债权人和债务人信息，用于刷新搜索结果
        const currentCreditor = creditor;
        const currentDebtor = debtor;

        // 重置表单
        resetForm();

        // 提交成功后刷新数据 - 参考新增债权记录的实现方式
        setTimeout(async () => {
          // 1. 刷新债权处置记录表 - 这是最重要的
          await fetchCurrentYearDisposalRecords();

          // 2. 触发定时任务更新后续月份数据
          try {
            const taskResponse = await api.post('/tasks/trigger-debt-update');
            if (taskResponse.status === 200) {
            }
          } catch (taskError) {}

          // 3. 如果之前有搜索结果，重新触发搜索以更新余额
          if (currentCreditor && currentDebtor) {
            await autoSearch(currentCreditor, currentDebtor);
          }
        }, 1000); // 使用1秒延迟，与新增债权记录保持一致
      } else {
        alert(`提交失败: ${response.statusText}`);
      }
    } catch (error) {
      console.error('提交过程中出错:', error);
      alert(`提交出错: ${error.message}`);
    }
  };

  // 重置表单
  const resetForm = () => {
    setDebtor('');
    setCreditor('');
    setManagementCompany('');
    setDebtPeriod('');
    setCaseName(''); // 重置案件名称
    setIsLitigation('否'); // 重置是否涉诉为“否”
    setDebtAmount(''); // 重置债权金额/本月末债权余额
    setDispositionAmount('');
    setDispositionDetails({
      cashAmount: '',
      assetAmount: '',
      inventoryAmount: '',
      adjustmentAmount: '',
      otherAmount: ''
    });
    setRemark('');
    setError('');
    setAmountError(''); // 重置金额错误提示
    setSearchResults([]);
    setHasSearched(false);
    setSelectedRows({});
    setDebtDetails(null); // 重置详细信息
  };

  // 帮助函数 - 自动设置当前年月
  useEffect(() => {
    // 如果年月字段为空，自动设置为当前年月
    if (!yearMonth) {
      const now = new Date();
      const currentYearMonth = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(
        2,
        '0'
      )}`;
      setYearMonth(currentYearMonth);
    }
  }, [yearMonth]);

  // 组件挂载时获取当年处置数据
  useEffect(() => {
    // 在组件加载时，获取当前年度的债权处置记录

    fetchCurrentYearDisposalRecords();

    // 空依赖数组确保只在组件挂载时执行一次
  }, []);

  return (
    <DashboardLayout>
      <DashboardNavbar />
      <Container maxWidth="lg">
        <Box sx={{ py: 2 }}>
          <Paper elevation={3}>
            <form onSubmit={handleSubmit} noValidate>
              <Grid container spacing={2} sx={{ p: 2 }}>
                {/* 搜索部分 */}
                <Grid item xs={12}>
                  <Card variant="outlined">
                    <CardContent sx={{ pb: '16px !important' }}>
                      <Typography
                        variant="h6"
                        sx={{
                          mb: 2,
                          fontSize: '18px',
                          fontWeight: 600,
                          color: '#1976d2'
                        }}
                      >
                        逾期债权处置
                      </Typography>
                      <Typography
                        variant="h6"
                        sx={{ mb: 2, fontSize: '15px', fontWeight: 600, color: '#1976d2' }}
                      >
                        基本信息
                      </Typography>
                      <Grid container spacing={2}>
                        <Grid item xs={12} sm={6}>
                          <FormInput
                            label="债权人"
                            value={creditor}
                            onChange={e => {
                              const newValue = e.target.value;
                              setCreditor(newValue);
                              if (newValue && debtor) {
                                // 当债权人和债务人都有值时，自动搜索
                                autoSearch(newValue, debtor);
                              }
                            }}
                            placeholder="请输入债权人名称"
                            required
                            autoComplete="on"
                            name="creditor"
                            sx={{ '& .MuiOutlinedInput-root': { height: '40px' } }}
                          />
                        </Grid>
                        <Grid item xs={12} sm={6}>
                          <FormInput
                            label="债务人"
                            value={debtor}
                            onChange={e => {
                              const newValue = e.target.value;
                              setDebtor(newValue);
                              if (creditor && newValue) {
                                // 当债权人和债务人都有值时，自动搜索
                                autoSearch(creditor, newValue);
                              }
                            }}
                            placeholder="请输入债务人名称"
                            required
                            autoComplete="on"
                            name="debtor"
                            sx={{ '& .MuiOutlinedInput-root': { height: '40px' } }}
                          />
                        </Grid>
                        {isSearching && (
                          <Grid
                            item
                            xs={12}
                            sx={{ display: 'flex', justifyContent: 'center', mt: 1 }}
                          >
                            <CircularProgress size={24} color="info" />
                          </Grid>
                        )}
                      </Grid>
                    </CardContent>
                  </Card>
                </Grid>

                {/* 搜索结果表格 */}
                {hasSearched && searchResults.length > 0 && (
                  <Grid item xs={12}>
                    <Card variant="outlined">
                      <CardContent sx={{ pb: '16px !important' }}>
                        <Typography
                          variant="h6"
                          sx={{
                            mb: 2,
                            fontSize: '15px',
                            fontWeight: 500,
                            color: '#1976d2'
                          }}
                        >
                          查询结果 ({searchResults.length} 条记录)
                        </Typography>

                        {/* 搜索结果表格 */}
                        <GenericDataTable
                          columns={[
                            { field: 'creditor', headerName: '债权人', width: '10%' },
                            { field: 'debtor', headerName: '债务人', width: '10%' },
                            { field: 'period', headerName: '归属期间', width: '8%' },
                            { field: 'isLitigation', headerName: '是否涉诉', width: '7%' },
                            { field: 'managementCompany', headerName: '管理公司', width: '9%' },
                            {
                              field: 'previousMonthBalance',
                              headerName: '本月初债权余额',
                              width: '10%',
                              valueFormatter: params => formatCurrency(params.value)
                            },
                            {
                              field: 'currentMonthNewDebt',
                              headerName: '本月新增债权',
                              width: '10%',
                              valueFormatter: params => formatCurrency(params.value)
                            },
                            {
                              field: 'currentMonthDisposeDebt',
                              headerName: '本月减少债权',
                              width: '10%',
                              valueFormatter: params => formatCurrency(params.value)
                            },
                            {
                              field: 'currentMonthBalance',
                              headerName: '本月末债权余额',
                              width: '10%',
                              valueFormatter: params => formatCurrency(params.value)
                            }
                          ]}
                          data={processedResults}
                          formatCurrency={formatCurrency}
                          compact={true}
                          rowHeight={40}
                          fontSize={13}
                          renderActions={
                            searchResults.length > 1
                              ? row => (
                                  <Box
                                    sx={{
                                      display: 'flex',
                                      justifyContent: 'center',
                                      alignItems: 'center',
                                      height: '100%'
                                    }}
                                    onClick={() => handleCheckboxClick(row.id)}
                                  >
                                    <Box
                                      sx={{
                                        width: '18px',
                                        height: '18px',
                                        border: '1px solid #aaa',
                                        borderRadius: '2px',
                                        display: 'flex',
                                        justifyContent: 'center',
                                        alignItems: 'center',
                                        bgcolor: row.selected ? '#1976d2' : 'transparent',
                                        color: 'white',
                                        cursor: 'pointer',
                                        transition: 'all 0.2s',
                                        '&:hover': {
                                          bgcolor: row.selected ? '#1565c0' : '#f5f5f5'
                                        }
                                      }}
                                    >
                                      {row.selected && '✓'}
                                    </Box>
                                  </Box>
                                )
                              : null
                          }
                          actionColumnWidth="6%"
                          actionColumnTitle="更新"
                        />
                      </CardContent>
                    </Card>
                  </Grid>
                )}

                {/* 处置时间和新增处置金额 */}
                <Grid item xs={12}>
                  <Card variant="outlined">
                    <CardContent sx={{ pb: '16px !important' }}>
                      <Grid container spacing={2}>
                        <Grid item xs={4}>
                          <Typography
                            variant="h6"
                            sx={{
                              mb: 2,
                              color: '#1976d2',
                              fontSize: '15px',
                              fontWeight: 500
                            }}
                          >
                            处置时间
                          </Typography>
                          <FormMonthPicker
                            label=""
                            value={yearMonth}
                            onChange={handleYearMonthChange}
                            name="yearMonth"
                            fullWidth
                          />
                        </Grid>
                        <Grid item xs={4}>
                          <Typography
                            variant="h6"
                            sx={{
                              mb: 2,
                              color: '#1976d2',
                              fontSize: '15px',
                              fontWeight: 500
                            }}
                          >
                            是否涉诉
                          </Typography>
                          <FormSelect
                            value={isLitigation}
                            onChange={e => setIsLitigation(e.target.value)}
                            options={[
                              { value: '是', label: '是' },
                              { value: '否', label: '否' }
                            ]}
                            SelectProps={{
                              sx: { '& .MuiOutlinedInput-root': { height: '40px' } }
                            }}
                          />
                        </Grid>
                        <Grid item xs={4}>
                          <Typography
                            variant="h6"
                            sx={{
                              mb: 2,
                              color: '#1976d2',
                              fontSize: '15px',
                              fontWeight: 500
                            }}
                          >
                            新增处置金额（万元） *
                          </Typography>
                          <Box sx={{ position: 'relative' }}>
                            <FormInput
                              type="number"
                              value={dispositionAmount}
                              onChange={handleChangeDispositionAmount}
                              required={false}
                              error={!!amountError}
                              autoComplete="off"
                              placeholder="请输入新增处置金额（不能为负数）"
                              sx={{ '& .MuiOutlinedInput-root': { height: '40px' } }}
                              isNumberFormat={true}
                              decimalPlaces={2}
                              warnings={inputWarnings}
                              onWarning={(label, warning) => {
                                setInputWarnings(prev => ({
                                  ...prev,
                                  [label]: warning
                                }));
                              }}
                            />
                            {amountError && (
                              <Typography
                                variant="caption"
                                sx={{
                                  position: 'absolute',
                                  right: 0,
                                  top: '100%',
                                  fontSize: '14px',
                                  fontWeight: 'bold',
                                  color: '#b71c1c' // 深红色（Material UI 的 red[900]）
                                }}
                              >
                                {amountError}
                              </Typography>
                            )}
                          </Box>
                        </Grid>
                      </Grid>
                    </CardContent>
                  </Card>
                </Grid>

                {/* 处置金额明细 */}
                <Grid item xs={12}>
                  <Card variant="outlined">
                    <CardContent sx={{ pb: '16px !important' }}>
                      <Typography
                        variant="h6"
                        sx={{
                          mb: 2,
                          color: '#1976d2',
                          fontSize: '15px',
                          fontWeight: 500
                        }}
                      >
                        处置金额明细（万元）
                      </Typography>

                      <Grid container spacing={2}>
                        {Object.keys(dispositionTypeLabels).map(key => (
                          <Grid item xs key={key}>
                            <TextField
                              label={dispositionTypeLabels[key]}
                              type="text"
                              value={dispositionDetails[key]}
                              onChange={e => handleChangeAmount(key, e.target.value)}
                              fullWidth
                              size="small"
                              required={false}
                              autoComplete="off"
                              inputProps={{ inputMode: 'decimal' }}
                              sx={{ '& .MuiOutlinedInput-root': { height: '40px' } }}
                            />
                          </Grid>
                        ))}

                        <Grid item xs>
                          <TextField
                            label="处置金额合计"
                            type="text"
                            value={calculateTotalDispositionAmount().toFixed(2)}
                            InputProps={{
                              readOnly: true
                            }}
                            fullWidth
                            size="small"
                            required={false}
                            autoComplete="off"
                            sx={{
                              '& .MuiOutlinedInput-root': { height: '40px' },
                              '& .MuiInputBase-input': {
                                color: (() => {
                                  const total = calculateTotalDispositionAmount();
                                  const dispAmount =
                                    dispositionAmount === '' ? 0 : parseFloat(dispositionAmount);
                                  return Math.abs(
                                    Math.round(total * 1000) / 1000 -
                                      Math.round(dispAmount * 1000) / 1000
                                  ) < 0.001
                                    ? '#2e7d32' // success.main
                                    : '#d32f2f'; // error.main
                                })(),
                                fontWeight: 'bold'
                              }
                            }}
                          />
                        </Grid>
                      </Grid>
                    </CardContent>
                  </Card>
                </Grid>

                {/* 备注信息 */}
                <Grid item xs={12}>
                  <Card variant="outlined">
                    <CardContent sx={{ pb: '16px !important' }}>
                      <Typography
                        variant="h6"
                        sx={{
                          mb: 2,
                          color: '#1976d2',
                          fontSize: '15px',
                          fontWeight: 500
                        }}
                      >
                        备注信息
                      </Typography>
                      <TextField
                        label="备注"
                        value={remark}
                        onChange={e => setRemark(e.target.value)}
                        fullWidth
                        multiline
                        rows={3}
                        placeholder="请输入处置相关备注信息..."
                        sx={{ '& .MuiOutlinedInput-root': { padding: '10px' } }}
                      />
                    </CardContent>
                  </Card>
                </Grid>

                {/* 错误提示 */}
                {error && (
                  <Grid item xs={12}>
                    <Alert
                      severity="error"
                      sx={{
                        backgroundColor: 'transparent',
                        boxShadow: 'none',
                        border: 'none',
                        '& .MuiPaper-root': {
                          backgroundColor: 'transparent'
                        },
                        '& .MuiAlert-message': {
                          padding: '6px 0',
                          color: '#b71c1c',
                          fontWeight: 'bold',
                          fontSize: '14px'
                        },
                        '& .MuiAlert-icon': {
                          color: '#b71c1c'
                        }
                      }}
                    >
                      {error}
                    </Alert>
                  </Grid>
                )}

                {/* 提交按钮 */}
                <Grid item xs={12}>
                  <Box sx={{ display: 'flex', justifyContent: 'center' }}>
                    <MDButton
                      variant="contained"
                      color="primary"
                      type="submit"
                      disabled={!!error || !!amountError}
                      sx={{
                        height: '40px',
                        minWidth: '120px',
                        fontSize: '1rem'
                      }}
                    >
                      提交更新
                    </MDButton>
                  </Box>
                </Grid>

                {/* 债权处置记录列表 */}
                <Grid item xs={12}>
                  <Paper
                    sx={{ mt: 4, p: 2, borderRadius: 1, boxShadow: '0 2px 8px 0 rgba(0,0,0,0.1)' }}
                  >
                    <Box
                      sx={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        alignItems: 'center',
                        mb: 2
                      }}
                    >
                      <Typography
                        variant="subtitle1"
                        sx={{ fontSize: '15px', fontWeight: 500, color: '#283593' }}
                      >
                        债权处置记录
                      </Typography>
                      <Button
                        variant="outlined"
                        size="small"
                        onClick={async () => {
                          await fetchCurrentYearDisposalRecords();
                        }}
                        sx={{ fontSize: '12px', padding: '4px 12px' }}
                      >
                        刷新记录
                      </Button>
                    </Box>
                    {isLoadingDisposalRecords && (
                      <Box sx={{ display: 'flex', justifyContent: 'center', my: 2 }}>
                        <CircularProgress size={24} color="info" />
                      </Box>
                    )}
                    <GenericDataTable
                      data={disposalRecords.length > 0 ? disposalRecords : []}
                      columns={[
                        {
                          field: 'id',
                          headerName: '序号',
                          width: '5%',
                          renderCell: (params, index) => {
                            return index + 1;
                          }
                        },
                        { field: 'creditor', headerName: '债权人', width: '20%' },
                        { field: 'debtor', headerName: '债务人', width: '20%' },
                        { field: 'managementCompany', headerName: '管理公司', width: '10%' },
                        { field: 'isLitigation', headerName: '是否涉诉', width: '10%' },
                        { field: 'month', headerName: '月份', width: '5%' },
                        {
                          field: 'disposalAmount',
                          headerName: '处置金额',
                          width: '10%',
                          renderCell: params => {
                            // 格式化金额显示
                            return formatCurrency(params.value).replace('¥', '');
                          }
                        }
                      ]}
                      pagination
                      rowHeight={40}
                      renderActions={params => {
                        return (
                          <Button
                            variant="text"
                            color="error"
                            size="small"
                            onClick={() => handleDeleteRecord(params)}
                            disabled={deletingRecordIds[params.id] || false}
                            sx={{
                              minWidth: 'auto',
                              padding: '4px 8px',
                              fontSize: '12px'
                            }}
                          >
                            {deletingRecordIds[params.id] ? '处理中...' : '删除'}
                          </Button>
                        );
                      }}
                      actionColumnWidth="10%"
                      actionColumnTitle="操作"
                      compact={true}
                      emptyMessage="暂无债权处置数据记录"
                    />
                  </Paper>
                </Grid>
              </Grid>
            </form>
          </Paper>
        </Box>
      </Container>
    </DashboardLayout>
  );
};

export default OverdueReductionUpdate;
