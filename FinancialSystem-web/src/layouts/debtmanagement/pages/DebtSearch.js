import React, { useState } from 'react';
import '../styles/table-styles.css';
import api from '../../../utils/api';
import {
  Box,
  Container,
  Paper,
  Typography,
  Grid,
  Button,
  Divider,
  TextField,
  Card,
  CircularProgress
} from '@mui/material';
import SearchIcon from '@mui/icons-material/Search';
import DashboardLayout from 'examples/LayoutContainers/DashboardLayout';
import DashboardNavbar from 'examples/Navbars/DashboardNavbar';
import GenericDataTable from 'components/tables/GenericDataTable';
// 自定义样式对象
const styles = {
  root: {
    '& .MuiTextField-root': {
      marginBottom: 2
    }
  },
  paper: {
    padding: 3,
    margin: '20px 0',
    borderRadius: 2,
    boxShadow: '0 4px 20px 0 rgba(0,0,0,0.1)',
    backgroundColor: '#ffffff'
  },
  title: {
    fontSize: '1.5rem',
    fontWeight: 600,
    color: '#1a237e',
    marginBottom: 3,
    textAlign: 'center'
  },
  searchButton: {
    padding: '10px 20px',
    backgroundColor: '#3f51b5',
    '&:hover': {
      backgroundColor: '#303f9f'
    },
    width: '50%'
  },
  resetButton: {
    padding: '10px 20px',
    backgroundColor: '#f44336',
    color: '#fff',
    '&:hover': {
      backgroundColor: '#d32f2f'
    },
    width: '50%'
  },
  searchField: {
    width: '100%'
  },
  noResults: {
    textAlign: 'center',
    padding: 4,
    color: '#757575'
  }
};

const DebtSearch = () => {
  const [searchParams, setSearchParams] = useState({
    creditor: '',
    debtor: ''
  });
  const [searchResults, setSearchResults] = useState([]);
  const [isSearching, setIsSearching] = useState(false);
  const [hasSearched, setHasSearched] = useState(false);

  // 列定义，用于 GenericDataTable
  const columns = [
    { field: 'creditor', headerName: '债权人', width: '20%' },
    { field: 'debtor', headerName: '债务人', width: '22%' },
    { field: 'period', headerName: '归属期间', width: '12%' },
    { field: 'isLitigation', headerName: '是否诉讼', width: '8%' },
    { field: 'managementCompany', headerName: '管理公司', width: '10%' },
    { field: 'newAmount', headerName: '新增金额', width: '10%', type: 'number' },
    { field: 'reductionAmount', headerName: '处置金额', width: '10%', type: 'number' },
    { field: 'debtBalance', headerName: '剩余债权', width: '10%', type: 'number' }
  ];

  // 处理输入框变化
  const handleInputChange = e => {
    const { name, value } = e.target;
    setSearchParams({ ...searchParams, [name]: value });
  };

  // 执行搜索
  const handleSearch = async e => {
    e.preventDefault();

    // 验证至少一个搜索条件
    if (!searchParams.creditor && !searchParams.debtor) {
      alert('请至少填写债权人或债务人其中一项进行搜索');
      return;
    }

    setIsSearching(true);
    setHasSearched(true);

    try {
      const queryParams = new URLSearchParams();
      if (searchParams.creditor) {
        queryParams.append('creditor', searchParams.creditor);
      }
      if (searchParams.debtor) {
        queryParams.append('debtor', searchParams.debtor);
      }

      const response = await api.get(`/debts/search?${queryParams.toString()}`);
      if (response.status === 200) {
        setSearchResults(response.data || []);
      }
    } catch (error) {
      console.error('搜索失败:', error);
      alert(`搜索失败：${error.response?.data?.message || error.message || '未知错误'}`);
      setSearchResults([]);
    } finally {
      setIsSearching(false);
    }
  };

  // 重置搜索
  const resetSearch = () => {
    setSearchParams({ creditor: '', debtor: '' });
    setSearchResults([]);
    setHasSearched(false);
  };

  // 格式化货币
  const formatCurrency = amount => {
    if (amount === null || amount === undefined) {
      return '-';
    }
    return new Intl.NumberFormat('zh-CN', {
      style: 'currency',
      currency: 'CNY',
      minimumFractionDigits: 2
    }).format(amount);
  };

  // 预处理数据，保证金额字段为数值
  const processedResults = searchResults.map((item, idx) => ({
    id: idx,
    creditor: item.creditor || '',
    debtor: item.debtor || '',
    period: item.period || '',
    isLitigation: item.isLitigation || '否',
    managementCompany: item.managementCompany || '',
    newAmount:
      typeof item.newAmount === 'number' ? item.newAmount : parseFloat(item.newAmount) || 0,
    reductionAmount:
      typeof item.reductionAmount === 'number'
        ? item.reductionAmount
        : parseFloat(item.reductionAmount) || 0,
    debtBalance:
      typeof item.debtBalance === 'number' ? item.debtBalance : parseFloat(item.debtBalance) || 0
  }));

  return (
    <DashboardLayout>
      <DashboardNavbar title="债权债务记录查询" />
      <Container maxWidth="lg">
        <Box sx={styles.root}>
          <Paper sx={styles.paper} elevation={0}>
            <Typography variant="h4" sx={styles.title}>
              债权债务记录查询
            </Typography>
            <Divider sx={{ mb: 4 }} />

            {/* 搜索表单 */}
            <form onSubmit={handleSearch}>
              <Grid container spacing={2}>
                {/* 债权人 */}
                <Grid item xs={11} md={4}>
                  <TextField
                    label="债权人"
                    name="creditor"
                    value={searchParams.creditor}
                    onChange={handleInputChange}
                    variant="outlined"
                    placeholder="请输入完整债权人名称"
                    sx={styles.searchField}
                  />
                </Grid>

                {/* 债务人 */}
                <Grid item xs={11} md={4}>
                  <TextField
                    label="债务人"
                    name="debtor"
                    value={searchParams.debtor}
                    onChange={handleInputChange}
                    variant="outlined"
                    placeholder="请输入完整债务人名称"
                    sx={styles.searchField}
                  />
                </Grid>

                {/* 搜索 & 重置按钮 */}
                <Grid item xs={12} md={4} sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Button
                    variant="contained"
                    startIcon={<SearchIcon />}
                    type="submit"
                    disabled={isSearching}
                    sx={styles.searchButton}
                  >
                    {isSearching ? <CircularProgress size={24} color="inherit" /> : '搜索'}
                  </Button>
                  <Button variant="contained" onClick={resetSearch} sx={styles.resetButton}>
                    重置
                  </Button>
                </Grid>
              </Grid>
            </form>

            {/* 搜索结果 */}
            {hasSearched && (
              <Card sx={{ mt: 4 }}>
                <Typography
                  variant="h6"
                  sx={{
                    p: 2,
                    backgroundColor: '#f9f9f9',
                    borderBottom: '1px solid #e0e0e0'
                  }}
                >
                  债权债务记录 ({searchResults.length} 条记录)
                </Typography>

                {searchResults.length > 0 ? (
                  <GenericDataTable
                    columns={columns}
                    data={processedResults}
                    formatCurrency={formatCurrency}
                  />
                ) : (
                  <Box sx={styles.noResults}>
                    <Typography variant="body1">未找到匹配的债权债务记录</Typography>
                    <Typography variant="body2" color="textSecondary" sx={{ mt: 1 }}>
                      请尝试调整债权人或债务人名称后重新搜索
                    </Typography>
                  </Box>
                )}
              </Card>
            )}
          </Paper>
        </Box>
      </Container>
    </DashboardLayout>
  );
};

export default DebtSearch;
