import React from 'react';
import { Grid, Card, CardContent, Typography } from '@mui/material';
import { styles } from '../styles';

// eslint-disable-next-line react/prop-types
const AssetStatistics = ({ assets }) => {
  return (
    <Grid container spacing={3} sx={{ mb: 4 }}>
      <Grid item xs={12} md={3}>
        <Card sx={styles.card}>
          <CardContent sx={styles.cardContent}>
            <Typography variant="h6" sx={styles.statValue}>
              {/* eslint-disable-next-line react/prop-types */}
              {assets.totalArea.toLocaleString()}㎡
            </Typography>
            <Typography sx={styles.statLabel}>总资产面积</Typography>
          </CardContent>
        </Card>
      </Grid>
      <Grid item xs={12} md={3}>
        <Card sx={styles.card}>
          <CardContent sx={styles.cardContent}>
            <Typography variant="h6" sx={styles.statValue}>
              {/* eslint-disable-next-line react/prop-types */}¥
              {(assets.totalValue / 10000).toLocaleString()}万
            </Typography>
            <Typography sx={styles.statLabel}>总资产价值</Typography>
          </CardContent>
        </Card>
      </Grid>
      <Grid item xs={12} md={3}>
        <Card sx={styles.card}>
          <CardContent sx={styles.cardContent}>
            <Typography variant="h6" sx={styles.statValue}>
              {/* eslint-disable-next-line react/prop-types */}
              {assets.selfUseArea.toLocaleString()}㎡
            </Typography>
            <Typography sx={styles.statLabel}>自用面积</Typography>
          </CardContent>
        </Card>
      </Grid>
      <Grid item xs={12} md={3}>
        <Card sx={styles.card}>
          <CardContent sx={styles.cardContent}>
            <Typography variant="h6" sx={styles.statValue}>
              {/* eslint-disable-next-line react/prop-types */}
              {assets.rentalArea.toLocaleString()}㎡
            </Typography>
            <Typography sx={styles.statLabel}>租赁面积</Typography>
          </CardContent>
        </Card>
      </Grid>
    </Grid>
  );
};

export default AssetStatistics;
