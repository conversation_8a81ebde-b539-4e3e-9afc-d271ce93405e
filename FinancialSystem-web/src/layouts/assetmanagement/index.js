import React, { useState } from 'react';
import { Box, Container, Paper, Typography, Grid, Divider } from '@mui/material';
import {
  Description as DescriptionIcon,
  Assessment as AssessmentIcon,
  Home as HomeIcon,
  Group as GroupIcon
} from '@mui/icons-material';
import DashboardLayout from 'examples/LayoutContainers/DashboardLayout';
import DashboardNavbar from 'examples/Navbars/DashboardNavbar';
import { styles } from './styles';
import { mockData } from './data/mockData';
import AssetStatistics from './components/AssetStatistics';
import AssetDetails from './components/AssetDetails';
import GroupAffairsCard from './components/GroupAffairsCard';
import AddItemDialog from './components/AddItemDialog';

const AssetManagement = () => {
  const [selectedTab, setSelectedTab] = useState(0);
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedModule, setSelectedModule] = useState(null);
  const [formData, setFormData] = useState({
    title: '',
    company: '',
    description: ''
  });

  const handleTabChange = (event, newValue) => {
    setSelectedTab(newValue);
  };

  const handleOpenDialog = module => {
    setSelectedModule(module);
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setFormData({ title: '', company: '', description: '' });
  };

  const handleSubmit = () => {
    // 这里添加提交逻辑
    handleCloseDialog();
  };

  return (
    <DashboardLayout>
      <DashboardNavbar />
      <Container maxWidth="lg">
        <Box sx={styles.root}>
          {/* 资产整理情况部分 */}
          <Paper sx={styles.paper} elevation={0}>
            <Typography variant="h4" sx={styles.title}>
              公司资产整理情况
            </Typography>
            <Divider sx={{ mb: 4 }} />

            <AssetStatistics assets={mockData.companies[0].assets} />

            <AssetDetails
              selectedTab={selectedTab}
              handleTabChange={handleTabChange}
              properties={mockData.companies[0].assets.properties}
              lands={mockData.companies[0].assets.lands}
            />
          </Paper>

          {/* 对接集团事项部分 */}
          <Paper sx={styles.paper} elevation={0}>
            <Typography variant="h4" sx={styles.title}>
              对接集团事项
            </Typography>
            <Divider sx={{ mb: 4 }} />

            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <GroupAffairsCard
                  title="资产报备"
                  icon={<DescriptionIcon color="primary" />}
                  items={mockData.assetReporting}
                  module="assetReporting"
                  onAddClick={handleOpenDialog}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <GroupAffairsCard
                  title="评估备案"
                  icon={<AssessmentIcon color="primary" />}
                  items={mockData.evaluationRecords}
                  module="evaluationRecords"
                  onAddClick={handleOpenDialog}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <GroupAffairsCard
                  title="产权登记"
                  icon={<HomeIcon color="primary" />}
                  items={mockData.propertyRegistration}
                  module="propertyRegistration"
                  onAddClick={handleOpenDialog}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <GroupAffairsCard
                  title="资产评审委员会"
                  icon={<GroupIcon color="primary" />}
                  items={mockData.reviewCommittee}
                  module="reviewCommittee"
                  onAddClick={handleOpenDialog}
                />
              </Grid>
            </Grid>
          </Paper>
        </Box>
      </Container>

      <AddItemDialog
        open={openDialog}
        onClose={handleCloseDialog}
        selectedModule={selectedModule}
        formData={formData}
        onFormChange={setFormData}
        onSubmit={handleSubmit}
      />
    </DashboardLayout>
  );
};

export default AssetManagement;
