/**
 * 债权转换相关API调用工具
 * 用于前端调用债权转换相关的后端接口
 * <AUTHOR>
 */

const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || 'http://localhost:8080';

/**
 * 搜索可转换的债权记录
 * @param {string} creditor - 债权人（可选）
 * @param {string} debtor - 债务人（可选）
 * @returns {Promise<Array>} 债权记录列表
 */
export const searchConvertibleDebts = async (creditor = '', debtor = '') => {
    try {
        const params = new URLSearchParams();
        if (creditor) params.append('creditor', creditor);
        if (debtor) params.append('debtor', debtor);
        
        const response = await fetch(`${API_BASE_URL}/api/debts/conversion/search?${params}`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem('jwt_token')}`
            }
        });
        
        if (!response.ok) {
            throw new Error(`搜索失败: ${response.status} ${response.statusText}`);
        }
        
        const data = await response.json();
        console.log('搜索结果:', data);
        return data;
        
    } catch (error) {
        console.error('搜索可转换债权记录失败:', error);
        throw error;
    }
};

/**
 * 诉讼转非诉讼
 * @param {Object} conversionData - 转换请求数据
 * @returns {Promise<Object>} 转换结果
 */
export const convertLitigationToNonLitigation = async (conversionData) => {
    try {
        console.log('诉讼转非诉讼请求数据:', conversionData);
        
        const response = await fetch(`${API_BASE_URL}/api/debts/conversion/litigation-to-non-litigation`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem('jwt_token')}`
            },
            body: JSON.stringify(conversionData)
        });
        
        const result = await response.json();
        console.log('诉讼转非诉讼响应:', result);
        
        if (!response.ok) {
            if (result.message) {
                throw new Error(result.message);
            } else {
                throw new Error(`转换失败: ${response.status} ${response.statusText}`);
            }
        }
        
        return result;
        
    } catch (error) {
        console.error('诉讼转非诉讼失败:', error);
        throw error;
    }
};

/**
 * 非诉讼转诉讼
 * @param {Object} conversionData - 转换请求数据
 * @returns {Promise<Object>} 转换结果
 */
export const convertNonLitigationToLitigation = async (conversionData) => {
    try {
        console.log('非诉讼转诉讼请求数据:', conversionData);
        
        const response = await fetch(`${API_BASE_URL}/api/debts/conversion/non-litigation-to-litigation`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem('jwt_token')}`
            },
            body: JSON.stringify(conversionData)
        });
        
        const result = await response.json();
        console.log('非诉讼转诉讼响应:', result);
        
        if (!response.ok) {
            if (result.message) {
                throw new Error(result.message);
            } else {
                throw new Error(`转换失败: ${response.status} ${response.statusText}`);
            }
        }
        
        return result;
        
    } catch (error) {
        console.error('非诉讼转诉讼失败:', error);
        throw error;
    }
};

/**
 * 验证转换请求数据
 * @param {Object} data - 要验证的数据
 * @param {string} conversionType - 转换类型: 'toNonLitigation' 或 'toLitigation'
 * @returns {Array} 验证错误列表，为空则验证通过
 */
export const validateConversionData = (data, conversionType) => {
    const errors = [];
    
    // 基本字段验证
    if (!data.creditor || data.creditor.trim() === '') {
        errors.push('债权人不能为空');
    }
    
    if (!data.debtor || data.debtor.trim() === '') {
        errors.push('债务人不能为空');
    }
    
    if (!data.period || data.period.trim() === '') {
        errors.push('期间不能为空');
    }
    
    if (!data.year || data.year < 2000 || data.year > 2100) {
        errors.push('年份必须是有效的年份');
    }
    
    if (!data.month || data.month < 1 || data.month > 12) {
        errors.push('月份必须是1-12之间的数字');
    }
    
    if (!data.conversionYear || data.conversionYear < 2000 || data.conversionYear > 2100) {
        errors.push('转换年份必须是有效的年份');
    }
    
    if (!data.conversionMonth || data.conversionMonth < 1 || data.conversionMonth > 12) {
        errors.push('转换月份必须是1-12之间的数字');
    }
    
    // 非诉讼转诉讼的特有验证
    if (conversionType === 'toLitigation') {
        if (!data.litigationCase || data.litigationCase.trim() === '') {
            errors.push('非诉讼转诉讼时诉讼案件名称为必填项');
        }
    }
    
    return errors;
};

/**
 * 格式化转换响应消息
 * @param {Object} response - API响应数据
 * @returns {string} 格式化后的消息
 */
export const formatConversionResponse = (response) => {
    if (!response) return '无响应数据';
    
    if (response.success) {
        return `✅ ${response.message}${response.details ? '\n详情: ' + response.details : ''}`;
    } else {
        return `❌ ${response.message}${response.details ? '\n详情: ' + response.details : ''}`;
    }
};