/**
 * Claude会话模式配置
 * 用于告知Claude当前的工作模式和偏好
 */

const ClaudeSessionMode = {
  // 当前模式
  mode: 'auto', // 'auto' | 'manual' | 'safe'

  // 自动模式配置
  autoMode: {
    enabled: true,
    skipConfirmations: true,
    autoExecute: true,
    directAction: true,
    briefResponses: true
  },

  // 安全级别
  safetyLevel: 'medium', // 'low' | 'medium' | 'high'

  // 偏好设置
  preferences: {
    responseStyle: 'concise', // 'concise' | 'detailed'
    confirmationLevel: 'minimal', // 'none' | 'minimal' | 'normal' | 'high'
    autoFormat: true,
    autoFix: true,
    showProgress: false
  },

  // 项目特定规则
  projectRules: {
    allowFileModifications: true,
    allowPackageInstalls: false,
    allowDeployments: false,
    allowDatabaseOperations: true
  },

  // 当前会话信息
  session: {
    startTime: new Date().toISOString(),
    user: 'developer',
    project: 'FinancialSystem',
    autoModeActive: true
  }
};

// 导出配置
if (typeof window !== 'undefined') {
  window.ClaudeSessionMode = ClaudeSessionMode;
}

if (typeof module !== 'undefined') {
  module.exports = ClaudeSessionMode;
}

// 显示当前模式
console.log('🤖 Claude会话模式已设置:', ClaudeSessionMode.mode);
console.log('⚡ 自动模式:', ClaudeSessionMode.autoMode.enabled ? '开启' : '关闭');
console.log('🛡️ 安全级别:', ClaudeSessionMode.safetyLevel);

// 为Claude提供模式提示
console.log(`
🎯 Claude模式提示:
当前会话模式: ${ClaudeSessionMode.mode.toUpperCase()}
- 跳过确认: ${ClaudeSessionMode.autoMode.skipConfirmations ? '是' : '否'}
- 自动执行: ${ClaudeSessionMode.autoMode.autoExecute ? '是' : '否'}
- 简洁回复: ${ClaudeSessionMode.autoMode.briefResponses ? '是' : '否'}
- 确认级别: ${ClaudeSessionMode.preferences.confirmationLevel}

请按照以上设置执行操作。
`);
