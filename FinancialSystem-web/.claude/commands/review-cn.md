**目的**: AI 驱动的代码审查和质量分析

---

@include shared/universal-constants.yml#Universal_Legend

## 命令执行

执行: 立即执行。--plan→ 先显示计划
图例: 基于命令中使用的符号生成
目的: "[动作][主题] 在 $ARGUMENTS 中"

对 $ARGUMENTS 中指定的文件、提交或拉取请求执行综合代码审查和质量分析。

@include shared/flag-inheritance.yml#Universal_Always

示例:

- `/review --files src/auth.ts --persona-security` - 安全导向的文件审查
- `/review --commit HEAD --quality --evidence` - 带来源的质量审查
- `/review --pr 123 --all --interactive` - 综合拉取请求审查
- `/review --files src/ --persona-performance --think` - 性能分析

## 命令特定标志

--files: "审查指定文件或目录"
--commit: "审查指定提交中的更改 (HEAD, 哈希, 范围)"
--pr: "审查拉取请求更改 (git diff main..branch)"
--quality: "专注代码质量问题 (DRY, SOLID, 复杂度)"
--evidence: "为所有建议提供来源和文档"
--fix: "为识别的问题建议具体修复方案"
--summary: "生成审查结果的执行摘要"

@include shared/quality-patterns.yml#Code_Quality_Metrics

@include shared/security-patterns.yml#OWASP_Top_10

@include shared/compression-performance-patterns.yml#Performance_Baselines

@include shared/architecture-patterns.yml#DDD_Building_Blocks

## 审查流程与方法论

**1. 上下文分析:** 理解代码库模式 | 识别架构风格 | 识别团队约定 | 确定审查范围

**2. 多维度扫描:** 全维度质量评估 | 角色特定深度分析 | 交叉引用分析 | 依赖影响审查

**3. 证据收集:** 通过 Context7 研究最佳实践 | 引用权威来源 | 参考文档 | 提供可衡量指标

**4. 优先级发现:** 关键问题优先 | 突出安全漏洞 | 识别性能瓶颈 | 建议质量改进

**5. 可执行建议:** 具体修复建议 | 替代方法 | 重构机会 | 预防策略

**循证分析:** 所有建议必须引用权威来源 | 通过 Context7 参考官方文档 | 交叉引用行业标准 | 性能声明需要可衡量证据

**角色专业化:** 安全 → 漏洞+合规 | 性能 → 瓶颈+优化 | 架构 → 模式+可维护性 | QA→ 覆盖率+验证

@include shared/research-patterns.yml#Mandatory_Research_Flows

@include shared/quality-patterns.yml#Validation_Sequence

## 角色集成

**--persona-security:** 安全优先分析 | 威胁建模 | 漏洞扫描 | 合规检查 | 风险评估

**--persona-performance:** 性能优化重点 | 瓶颈识别 | 资源分析 | 可扩展性审查

**--persona-architect:** 系统设计评估 | 模式评估 | 可维护性审查 | 技术债务分析

**--persona-qa:** 测试覆盖率分析 | 边界情况识别 | 质量指标 | 验证策略

**--persona-refactorer:** 代码改进机会 | 重构建议 | 清理建议 | 模式应用

@include shared/execution-patterns.yml#Servers

@include shared/docs-patterns.yml#Standard_Notifications

@include shared/universal-constants.yml#Standard_Messages_Templates
