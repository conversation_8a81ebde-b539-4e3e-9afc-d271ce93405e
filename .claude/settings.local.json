{"permissions": {"allow": ["mcp__filesystem__list_directory", "mcp__filesystem__directory_tree", "mcp__filesystem__list_allowed_directories", "mcp__filesystem__read_file", "mcp__filesystem__read_multiple_files", "mcp__filesystem__search_files", "mcp__jetbrains__*", "mcp__database__*", "mcp__puppeteer__*", "mcp__playwright__*", "Bash(awk:*)", "Bash(bash:*)", "Bash(brew list:*)", "<PERSON><PERSON>(cat:*)", "<PERSON><PERSON>(chmod:*)", "Bash(claude --version)", "<PERSON><PERSON>(claude config --help)", "<PERSON><PERSON>(claude config list:*)", "Bash(claude update)", "Bash(cp:*)", "<PERSON><PERSON>(curl:*)", "<PERSON><PERSON>(docker:*)", "<PERSON><PERSON>(docker-compose:*)", "<PERSON><PERSON>(docker save:*)", "Bash(echo $SHELL)", "Bash(echo $TERM)", "<PERSON><PERSON>(echo:*)", "Bash(ESLINT_NO_DEV_ERRORS=true npm run build)", "Bash(find:*)", "Bash(git add:*)", "Bash(git checkout:*)", "Bash(git commit:*)", "Bash(git fetch:*)", "Bash(git merge:*)", "Bash(git pull:*)", "Bash(git push:*)", "Bash(git rm:*)", "Bash(git stash:*)", "Bash(grep:*)", "Bash(kill:*)", "Bash(ls:*)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(mv:*)", "<PERSON><PERSON>(mvn:*)", "<PERSON><PERSON>(mysql:*)", "Bash(npm install:*)", "Bash(npm ls:*)", "Bash(npm run:*)", "Bash(npm run build:*)", "Bash(npm run format:*)", "Bash(npm run lint)", "Bash(npm run lint:*)", "Bash(npm run quality:check:*)", "Bash(npm start)", "Bash(npm test:*)", "Bash(npm uninstall:*)", "Bash(npm view:*)", "Bash(npx:*)", "<PERSON><PERSON>(npx source-map-explorer:*)", "Bash(npx webpack-bundle-analyzer:*)", "<PERSON><PERSON>(open:*)", "Bash(pip install:*)", "<PERSON><PERSON>(pkill:*)", "<PERSON><PERSON>(python3:*)", "Bash(rg:*)", "Bash(rm:*)", "<PERSON><PERSON>(rmdir:*)", "Bash(rsync:*)", "<PERSON><PERSON>(scp:*)", "<PERSON><PERSON>(sed:*)", "<PERSON><PERSON>(source:*)", "Bash(ssh:*)", "<PERSON><PERSON>(timeout 30 mvn spring-boot:run)", "<PERSON><PERSON>(touch:*)", "Bash(tree:*)", "<PERSON><PERSON>(true)", "<PERSON><PERSON>(tty)", "Bash(uvx:*)", "Bash(xargs:*)", "Bash(./ci-cd/deploy/auto-deploy-fixed.sh:*)", "Bash(./ci-cd/deploy/auto-deploy-trigger.sh:*)", "Bash(./ci-cd/deploy/basic-deploy.sh:*)", "Bash(./ci-cd/deploy/deployment-monitor.sh:*)", "Bash(./ci-cd/deploy/enhanced-auto-deploy.sh:*)", "Bash(./ci-cd/deploy/local-deploy.sh:*)", "Bash(./ci-cd/deploy/prepare-docker-images.sh:*)", "Bash(./ci-cd/deploy/simple-deploy.sh:*)", "Bash(./scripts/deploy/deploy.sh:*)", "Bash(./test-auto-deploy.sh:*)", "WebFetch(domain:docs.anthropic.com)", "WebFetch(domain:e-cloudstore.com)", "WebFetch(domain:github.com)", "mcp__filesystem__create_directory", "Bash(timeout 30 npm run build)", "<PERSON><PERSON>(pip show:*)", "<PERSON><PERSON>(pip3 show:*)", "Bash(./scripts/migrate-to-english-db.sh:*)", "Bash(gemini-cli:*)", "Bash(./gemini-cli --help)", "<PERSON><PERSON>(git clone:*)", "Bash(npm link:*)", "Bash(export:*)", "Bash(./scripts/database/migrate-chinese-to-english-db.sh:*)", "Bash(./scripts/database/test-english-db-connection.sh:*)", "Bash(./scripts/database/check-english-db-sync.sh:*)", "Bash(./scripts/database/fix-english-db-sync.sh:*)", "Bash(brew services:*)", "Bash(ping:*)", "Bash(./install.sh)", "Bash(./scripts/database/comprehensive-sync-analysis.sh:*)", "Bash(./scripts/database/three-layer-sync-validation.sh:*)", "Bash(timeout 180 mvn spring-boot:run -pl api-gateway -Dspring-boot.run.jvmArguments=\"-Xms512m -Xmx1024m\")", "Bash(./scripts/database/execute-all-fixes.sh:*)", "Bash(./scripts/database/fix-mysql-authentication.sh:*)", "Bash(PORT=3001 npm start)", "Bash(git reset:*)", "Bash(yarn install)", "Bash(java:*)", "WebFetch(domain:raw.githubusercontent.com)", "mcp__filesystem__write_file", "Bash(node:*)", "Bash(npm audit:*)", "Bash(npm i:*)", "Bash(timeout 10 npm start:*)", "Bash(git log:*)", "Bash(nc:*)", "Bash(./scripts/database/sync-monitor.sh:*)", "Bash(./scripts/database/manual-sync-solution.sh:*)", "Bash(git ls-tree:*)", "Bash(git branch:*)", "<PERSON><PERSON>(unison:*)", "Bash(/Applications/Tailscale.app/Contents/MacOS/Tailscale status)", "Bash(mount:*)", "<PERSON><PERSON>(smbutil statshares:*)", "Bash(defaults read:*)", "Bash(traceroute:*)", "Bash(./scripts/manage-snapshots.sh list:*)", "Bash(./scripts/manage-snapshots.sh:*)", "Bash(./scripts/init-snapshots-simple.sh:*)", "Bash(./scripts/capture-real-snapshots.sh:*)", "mcp__ide__getDiagnostics", "Bash(git for-each-ref:*)", "<PERSON><PERSON>(git shortlog:*)", "Bash(./execute_triggers.sh)", "<PERSON><PERSON>(claude help)", "<PERSON><PERSON>(claude-code --version)", "<PERSON><PERSON>(claude doctor)", "Bash(telnet:*)", "Bash(sudo systemsetup:*)", "Bash(systemsetup:*)", "<PERSON><PERSON>(sudo launchctl:*)", "Bash(sudo pfctl:*)", "Bash(tailscale:*)", "<PERSON><PERSON>(dscl:*)", "Bash(pmset:*)", "<PERSON><PERSON>(sudo pmset:*)", "Bash(system_profiler:*)", "Bash(caffeinate:*)", "<PERSON><PERSON>(launchctl:*)", "Bash(/Applications/Tailscale.app/Contents/MacOS/Tailscale ip -4)"], "deny": []}}