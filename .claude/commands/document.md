**目的**: 专业文档创建

---

@include shared/universal-constants.yml#Universal_Legend

## 命令执行
执行: 立即执行。--plan→先显示计划
图例: 基于命令中使用的符号生成
目的: "[动作][主题] 在 $ARGUMENTS 中"

为 $ARGUMENTS 中指定的代码、API或系统生成综合文档。

@include shared/flag-inheritance.yml#Universal_Always

示例:
- `/document --type api --format openapi` - 生成API文档
- `/document --type readme --style detailed` - 创建详细的README
- `/document --type user --style tutorial` - 带教程的用户指南

文档模式:

**--type:** 文档类型
- api: API文档（OpenAPI/Swagger）| code: 代码文档（JSDoc/文档字符串）
- readme: 项目README文件 | architecture: 系统架构文档
- user: 最终用户文档 | dev: 开发者指南

**--format:** 输出格式
- markdown: Markdown格式（默认）| html: HTML文档
- pdf: PDF输出 | docusaurus: 兼容Docusaurus | mkdocs: 兼容MkDocs

**--style:** 文档风格
- concise: 简洁，仅包含基本信息 | detailed: 详细，包含示例
- tutorial: 分步指南格式 | reference: API参考风格

@include shared/docs-patterns.yml#Project_Documentation

@include shared/docs-patterns.yml#Standard_Notifications

@include shared/universal-constants.yml#Standard_Messages_Templates