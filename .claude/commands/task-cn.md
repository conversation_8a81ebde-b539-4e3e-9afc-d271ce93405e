**目的**: 跨会话复杂功能管理

---

@include shared/universal-constants.yml#Universal_Legend

## 命令执行
执行: 立即执行。--plan→先显示计划
图例: 基于命令中使用的符号生成
目的: "[动作][主题] 在 $ARGUMENTS 中"

通过自动分解、上下文保存和恢复功能跨会话管理复杂功能和需求。

@include shared/flag-inheritance.yml#Universal_Always

示例:
- `/task:create "实施OAuth 2.0认证系统"` - 创建复杂功能任务
- `/task:status oauth-task-id` - 检查任务状态
- `/task:resume oauth-task-id` - 中断后恢复工作
- `/task:update oauth-task-id "发现库冲突"` - 更新发现

## 操作

/task:create [描述]:
- 创建新任务并自动分解
- 生成子任务和里程碑
- 设置跟踪结构
- 初始化上下文保存

/task:update [任务ID] [更新]:
- 更新任务进度
- 修改需求
- 调整时间线
- 添加发现

/task:status [任务ID]:
- 显示当前进度
- 列出已完成子任务
- 显示阻塞问题
- 估计剩余工作

/task:resume [任务ID]:
- 加载任务上下文
- 从最后点继续
- 恢复工作状态
- 更新进度

/task:complete [任务ID]:
- 标记任务完成
- 生成摘要
- 归档产物
- 创建文档

## 任务结构

@include shared/task-management-patterns.yml#Task_Management_Hierarchy

任务组件:
- 标题和描述
- 验收标准
- 技术要求
- 子任务分解
- 进度跟踪
- 上下文保存

## 自动功能

智能分解:
- 分析复杂度
- 创建子任务
- 识别依赖关系
- 估计工作量
- 设定里程碑

上下文保存:
- 保存工作状态
- 跟踪决策
- 存储代码更改
- 维护历史
- 启用恢复

进度跟踪:
- 自动更新
- 跟踪阻塞问题
- 监控速度
- 调整估计
- 报告状态

## 恢复系统

@include shared/session-recovery.yml#Recovery_Patterns

会话恢复:
- 自动检测未完成任务
- 加载之前上下文
- 从检查点恢复
- 保持连续性
- 保持动力

## 最佳实践

任务创建:
- 明确需求
- 可测量结果
- 现实范围
- 定义边界
- 成功标准

任务管理:
- 定期更新
- 早期跟踪阻塞问题
- 记录决策
- 增量测试
- 沟通进度

## 示例

```bash
# 创建复杂功能任务
/task:create "实施OAuth 2.0认证系统"

# 检查任务状态
/task:status oauth-task-id

# 中断后恢复工作
/task:resume oauth-task-id

# 更新发现
/task:update oauth-task-id "发现库冲突，切换方法"

# 完成并生成摘要
/task:complete oauth-task-id
```

## 集成

配合使用:
- TodoWrite 用于子任务
- Git 用于版本控制
- Test 用于验证
- Document 用于产物
- 所有开发命令

## 交付物

- 任务分解文档
- 进度跟踪报告
- 技术决策日志
- 实施产物
- 完成摘要
- 学习总结

@include shared/universal-constants.yml#Standard_Messages_Templates