**目的**: 专业开发环境配置

---

@include shared/universal-constants.yml#Universal_Legend

## 命令执行
执行: 立即执行。--plan→先显示计划
图例: 基于命令中使用的符号生成
目的: "[动作][主题] 在 $ARGUMENTS 中"

为 $ARGUMENTS 配置综合开发环境和CI/CD流水线。

@include shared/flag-inheritance.yml#Universal_Always

示例:
- `/dev-setup --type node --ci github --tools` - 带GitHub Actions的Node.js项目
- `/dev-setup --type python --tools --think` - 带综合工具的Python项目
- `/dev-setup --type monorepo --ci gitlab --think-hard` - 带GitLab CI的全栈单体仓库
- `/dev-setup --type react --tools --ci github` - 带质量工具的React项目

## 配置类型

--type 标志:
- node: Node.js/TypeScript项目配置
- python: Python虚拟环境和工具
- react: React + Vite/Next.js配置
- fullstack: 完整全栈环境
- monorepo: 多包单体仓库配置

--ci 标志:
- github: GitHub Actions工作流
- gitlab: GitLab CI/CD流水线
- jenkins: Jenkins流水线配置
- circleci: CircleCI配置
- custom: 自定义CI/CD解决方案

--tools 标志:
- 包含开发工具：linters、formatters、pre-commit钩子
- 配置VS Code设置和扩展
- 设置调试配置
- 安装推荐工具

## 配置组件

环境配置:
- 包管理器设置 (npm/yarn/pnpm)
- 版本管理 (.nvmrc, .python-version)
- 环境变量和.env结构
- 必要时的Docker配置

代码质量:
- ESLint/Prettier配置
- Pre-commit钩子 (husky, lint-staged)
- 测试框架设置 (Jest, Pytest等)
- 代码覆盖率配置

CI/CD流水线:
- 构建和测试工作流
- 部署配置
- 安全扫描 (SAST/DAST)
- 依赖漏洞检查
- 发布自动化

开发工具:
- VS Code工作区设置
- 调试配置
- 任务运行器和脚本
- 文档生成

## 最佳实践

安全性:
- 永不提交密钥或凭据
- 对敏感数据使用环境变量
- 在CI中配置安全扫描
- 实施依赖漏洞检查

性能:
- 在CI中缓存依赖
- 并行化测试执行
- 优化构建过程
- 使用适当的资源限制

可维护性:
- 团队间一致的工具
- 清晰的文档
- 自动化质量检查
- 可重现的环境

## 示例

```bash
# 带GitHub Actions的Node.js项目
/dev-setup --type node --ci github --tools

# 带综合工具的Python项目
/dev-setup --type python --tools --think

# 带GitLab CI的全栈单体仓库
/dev-setup --type monorepo --ci gitlab --think-hard

# 带所有质量工具的React项目
/dev-setup --type react --tools --ci github
```

## 交付物

- 完整的环境配置文件
- CI/CD流水线定义
- 开发工具配置
- 设置文档和README更新
- 常见开发任务的脚本

@include shared/universal-constants.yml#Standard_Messages_Templates