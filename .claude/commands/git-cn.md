**目的**: 带检查点管理的Git工作流程

---

@include shared/universal-constants.yml#Universal_Legend

## 命令执行
执行: 立即执行。--plan→先显示计划
图例: 基于命令中使用的符号生成
目的: "[动作][主题] 在 $ARGUMENTS 中"

管理 $ARGUMENTS 中指定仓库的全面git工作流程。

@include shared/flag-inheritance.yml#Universal_Always

示例:
- `/git --commit "Add user profile API endpoint"` - 带消息的标准提交
- `/git --pr --reviewers alice,bob --labels api,feature` - 创建带审阅者的PR
- `/git --flow feature "payment-integration" --think` - 完整功能工作流程
- `/git --pre-commit` - 设置pre-commit框架和基本钩子
- `/git --commit "Fix validation logic" --pre-commit` - 带pre-commit验证的提交
- `/git --pre-commit --security` - 设置包含安全钩子

Git操作:

**--commit:** 暂存合适的文件 | 生成有意义的提交消息 | 包含共同作者标识 | 遵循约定式提交

**--pr:** 创建拉取请求 | 生成PR描述 | 设置审阅者和标签 | 关联相关问题

**--flow:** Git工作流程模式
- feature: 功能分支工作流程 | hotfix: 紧急修复工作流程
- release: 发布分支工作流程 | gitflow: 完整GitFlow模型

**--pre-commit:** 设置和管理pre-commit钩子 | 自动安装框架 | 配置质量检查 | 提交前运行钩子

@include shared/execution-patterns.yml#Git_Integration_Patterns

@include shared/pre-commit-patterns.yml#Pre_Commit_Setup

@include shared/docs-patterns.yml#Standard_Notifications

@include shared/universal-constants.yml#Standard_Messages_Templates