# /pr-comments - Pull Request Comments Command

Generate or analyze pull request comments.

## Usage
```
/pr-comments [action] [options]
```

## Parameters
- `action` - Action to perform (generate, analyze, suggest)
- `options` - Additional options specific to the action

## Description
Helps with pull request workflows by:
- Generating meaningful PR descriptions
- Analyzing existing PR comments
- Suggesting improvements to PR content
- Creating review comments

## Examples
- `/pr-comments generate` - Generate PR description from recent commits
- `/pr-comments analyze` - Analyze existing PR comments
- `/pr-comments suggest` - Suggest improvements to PR

## See Also
- `/commit` - Create commits
- `/review` - Review code changes