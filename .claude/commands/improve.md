**Purpose**: Evidence-based enhancement and optimization

---

@include shared/universal-constants.yml#Universal_Legend

## Command Execution
Execute: immediate. --plan→show plan first
Legend: Generated based on symbols used in command
Purpose: "[Action][Subject] in $ARGUMENTS"

Systematically improve code quality, performance, and architecture in $ARGUMENTS using best practices and optimization techniques.

@include shared/flag-inheritance.yml#Universal_Always

Examples:
- `/improve --quality` - Code quality improvements
- `/improve --perf --iterate` - Performance optimization
- `/improve --arch --think-hard` - Architecture refactoring

## Command-Specific Flags
--quality: "Code quality improvements (readability, maintainability, DRY)"
--perf: "Performance optimizations (algorithms, caching, queries)"
--arch: "Architecture improvements (patterns, coupling, scalability)"
--refactor: "Safe refactoring preserving behavior"
--iterate: "Iterative improvement until threshold met"
--threshold: "Quality threshold (low|medium|high|perfect)"
--metrics: "Show before/after metrics"
--safe: "Conservative mode - only safe changes"

## Improvement Categories

**Code Quality:** Naming clarity | Function extraction | Duplication removal | Complexity reduction | Error handling | Type safety

**Performance:** Algorithm optimization | Query optimization | Caching strategies | Lazy loading | Memory efficiency | Parallel processing

**Architecture:** Design patterns | Dependency injection | Layer separation | Module boundaries | API design | Scalability patterns

**Maintainability:** Documentation | Test coverage | Configuration extraction | Magic number removal | Dead code elimination

## Improvement Process

**1. Analysis:** Current state assessment | Identify improvement areas | Prioritize by impact | Set measurable goals

**2. Planning:** Safe refactoring path | Preserve functionality | Incremental changes | Rollback strategy

**3. Implementation:** Small atomic changes | Continuous testing | Performance monitoring | Code review ready

**4. Validation:** Behavior preservation | Performance gains | Quality metrics | Regression testing

@include shared/quality-patterns.yml#Code_Quality_Metrics

## Deliverables

**Improved Code:** Refactored files | Preserved functionality | Enhanced quality | Better performance

**Improvement Report:** Before/after metrics | Changes summary | Performance gains | Quality improvements

**Documentation:** Refactoring decisions | Architecture changes | Performance optimizations | Future recommendations

@include shared/universal-constants.yml#Standard_Messages_Templates