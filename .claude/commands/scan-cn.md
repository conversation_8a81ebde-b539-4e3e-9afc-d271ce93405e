**目的**: 安全审计和验证

---

@include shared/universal-constants.yml#Universal_Legend

## 命令执行
执行: 立即执行。--plan→先显示计划
图例: 基于命令中使用的符号生成
目的: "[动作][主题] 在 $ARGUMENTS 中"

对 $ARGUMENTS 中指定的代码执行全面的安全、质量和依赖扫描。

@include shared/flag-inheritance.yml#Universal_Always

示例:
- `/scan --security` - 安全漏洞扫描
- `/scan --deps` - 依赖审计
- `/scan --validate` - 完整验证扫描
- `/scan --quick` - 快速扫描关键问题

## 命令特定标志
--security: "深度安全漏洞扫描（OWASP、CVE、机密信息）"
--deps: "依赖漏洞审计及修复建议"
--validate: "综合验证（语法、类型、逻辑、安全）"
--quick: "快速扫描，仅关注关键问题"
--fix: "自动修复安全问题"
--strict: "零容忍模式（任何问题都失败）"
--report: "生成详细报告"
--ci: "CI友好输出格式"

## 扫描类型

**安全扫描:** OWASP前10项 | 注入漏洞 | 认证缺陷 | 敏感数据暴露 | 硬编码机密 | CVE数据库检查

**依赖扫描:** 已知漏洞 | 过期包 | 许可证合规 | 供应链风险 | 传递依赖

**代码质量:** 复杂度指标 | 代码重复 | 死代码 | 类型安全 | 最佳实践 | 性能反模式

**配置:** 错误配置服务 | 不安全默认值 | 缺少安全头 | 暴露端点 | 弱加密

## 验证级别

**快速（--quick）:** 仅关键安全问题 | 已知CVE | 硬编码机密 | SQL注入 | XSS漏洞

**标准（默认）:** 所有安全检查 | 主要质量问题 | 依赖漏洞 | 配置问题

**严格（--strict）:** 所有问题加次要问题 | 样式违规 | 文档缺失 | 测试覆盖率 | 性能警告

@include shared/security-patterns.yml#OWASP_Top_10

## 交付物

**报告:** `.claudedocs/scans/security-{timestamp}.md` | 严重程度分类 | 修复建议 | 风险评估

**修复脚本:** 自动生成补丁 | 安全自动修复 | 手动修复说明 | 回滚程序

**CI集成:** 退出代码 | JSON输出 | SARIF格式 | GitHub/GitLab集成

@include shared/universal-constants.yml#Standard_Messages_Templates