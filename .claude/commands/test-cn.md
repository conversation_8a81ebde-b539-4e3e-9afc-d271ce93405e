**目的**: FinancialSystem 全面测试框架

---

@include shared/universal-constants.yml#Universal_Legend

## 命令执行
执行: 立即执行。--plan→先显示计划
图例: 基于命令中使用的符号生成
目的: "[动作][主题] 在 $ARGUMENTS 中"

使用专为FinancialSystem设计的测试框架进行全面测试，包括数据一致性验证、快照测试和回归防护。

@include shared/flag-inheritance.yml#Universal_Always

示例:
- `/test --init` - 初始化测试快照基准
- `/test --consistency` - 运行数据一致性测试
- `/test --snapshot` - 验证快照一致性
- `/test --update` - 更新快照基准（业务逻辑变更后）
- `/test --coverage` - 生成覆盖率报告
- `/test --integration` - 运行集成测试
- `/test --unit` - 运行单元测试
- `/test --regression` - 完整回归测试套件

## 🎯 FinancialSystem 专用命令标志

### 核心测试类型
--init: "初始化测试快照和基准数据（首次运行）"
--consistency: "运行跨表数据一致性验证测试"
--snapshot: "验证查询结果快照一致性"
--regression: "完整回归测试，防止功能破坏"

### 快照管理
--update: "更新快照基准（业务逻辑合理变更后）"
--verify: "验证所有快照的一致性"
--list: "列出所有现有快照"
--clean: "清理过期快照文件"
--reset: "重置所有快照（删除并重新创建）"

### 传统测试类型
--unit: "运行单元测试（Service、Repository层）"
--integration: "运行集成测试（API、数据库交互）"
--e2e: "运行端到端测试（完整业务流程）"
--tdd: "测试驱动开发工作流"

### 测试配置
--coverage: "生成详细覆盖率报告及未覆盖行"
--watch: "文件更改时持续运行测试"
--parallel: "并行运行测试（提高性能）"
--bail: "首次测试失败时停止"
--verbose: "详细输出测试过程信息"

## 🏗️ 测试框架架构

### 测试模块结构
```
FinancialSystem/
├── test-framework/              # 🔧 独立测试框架模块
│   ├── test-core/              # 核心测试工具和基类
│   │   ├── base/               # BaseUnitTest, BaseIntegrationTest, BaseConsistencyTest
│   │   ├── utils/              # TestDataCleaner, TestTransactionManager
│   │   ├── data/               # TestDataFactory, ConsistencyChecker
│   │   └── snapshot/           # SnapshotManager, SnapshotVerifier
│   └── test-data/              # 测试数据集
│       ├── baseline/           # 基准数据
│       ├── scenarios/          # 场景数据
│       └── snapshots/          # 查询结果快照
├── api-gateway/src/test/       # API层测试
├── services/*/src/test/        # 业务逻辑测试
└── shared/*/src/test/          # 共享组件测试
```

### 测试基类继承
- **BaseUnitTest**: Mockito支持的单元测试基类
- **BaseIntegrationTest**: Spring Boot完整上下文集成测试
- **BaseConsistencyTest**: 数据一致性和快照测试基类

## 📊 数据一致性测试

### 快照测试机制
```java
// 自动创建/验证快照
@Test
void verify_debtQuery_consistency() {
    List<DebtStats> results = debtService.queryStatistics(params);
    
    SnapshotVerifier.verify("debt_query_baseline", results)
        .withPrecision(BigDecimal.class, 2)
        .assertMatches();
}
```

### 跨表一致性检查
```java
// 验证4表数据同步
@Test
void verify_crossTable_consistency() {
    executeWithConsistencyCheck("add_debt", () -> {
        return debtService.addDebt(request);
    });
}
```

### 月度累计计算验证
```java
// 验证月度数据累加逻辑
@Test
void verify_monthly_accumulation() {
    Map<Integer, BigDecimal> expected = TestDataFactory.createMonthlyDistribution();
    BigDecimal actual = debtService.getAccumulatedAmount(2024, 6);
    assertEquals(calculateExpected(expected, 6), actual);
}
```

## 🛠️ 快速操作指南

### 首次使用（快照初始化）
```bash
# 方式1: 使用管理脚本
./scripts/manage-snapshots.sh init

# 方式2: 使用Maven命令
mvn test -Dtest=AutoSnapshotDemo#captureBaselineSnapshots -Dsnapshot.init=force

# 方式3: 使用Claude命令
/test --init
```

### 日常开发验证
```bash
# 验证数据一致性
/test --consistency

# 验证快照
/test --snapshot

# 完整回归测试
/test --regression
```

### 业务逻辑变更后
```bash
# 更新快照基准
/test --update

# 或使用管理脚本
./scripts/manage-snapshots.sh update
```

### 测试覆盖率检查
```bash
# 生成覆盖率报告
/test --coverage

# 检查特定模块
/test api-gateway --coverage
```

## 🎯 核心测试场景

### 债权管理测试
- **新增债权**: 4表同步更新验证
- **债权处置**: 处置方式和余额计算
- **查询统计**: 复杂条件查询结果一致性
- **月度累计**: 累加计算逻辑验证

### 数据一致性验证
- **跨表关联**: 新增表、处置表、减值准备表、诉讼表
- **金额计算**: 总额、处置额、余额的计算逻辑
- **月度数据**: 12个月份字段的累加验证
- **业务规则**: 涉诉/非涉诉、公司维度等业务约束

### 安全认证测试
- **JWT认证**: Token生成、验证、过期处理
- **权限控制**: 角色权限、API访问控制
- **数据安全**: 敏感信息保护、SQL注入防护

## 📋 测试数据管理

### 测试数据类型
- **基准数据**: 稳定的参考数据集（v1.0, v1.1, v1.2...）
- **场景数据**: 特定测试场景（正常流程、边界条件、异常情况）
- **性能数据**: 大数据量性能测试（>10000条记录）
- **快照数据**: 查询结果快照（JSON格式存储）

### 数据隔离策略
```java
// 使用命名空间隔离
String namespace = getTestNamespace(); // "TestClass_timestamp"
DebtRecord debt = TestDataFactory.debtRecord()
    .withCreditor(namespace + "_CREDITOR")
    .build();
```

### 自动清理机制
- 测试后自动清理以"TEST_"开头的数据
- 保留基准数据不被清理
- 支持事务回滚清理

## 🚀 CI/CD集成

### GitHub Actions配置
```yaml
# 自动运行测试
- name: Run Consistency Tests
  run: mvn test -Dtest=*ConsistencyTest

# 生成覆盖率报告
- name: Generate Coverage Report
  run: mvn jacoco:report

# 上传测试结果
- name: Upload Test Results
  uses: actions/upload-artifact@v3
  with:
    name: test-results
    path: target/surefire-reports/
```

### 质量门禁
- 整体覆盖率 > 80%
- 核心业务逻辑覆盖率 > 90%
- 新增代码覆盖率 > 95%
- 数据一致性测试必须通过

## 📈 测试最佳实践

### 测试命名规范
```java
// 单元测试：should_expectedBehavior_when_condition
void should_calculateInterest_when_validRateProvided()

// 集成测试：test_scenario_expectedOutcome  
void test_debtLifecycle_successfulCompletion()

// 一致性测试：verify_aspect_condition
void verify_crossTableConsistency_afterDebtAddition()
```

### 断言增强
```java
// 使用专用断言
DebtAssertions.assertDebtValid(debt);
ConsistencyAssertions.assertTablesConsistent(operation);

// 流式断言
assertThat(result)
    .isNotNull()
    .hasFieldOrPropertyWithValue("creditor", "测试债权人")
    .matches(debt -> debt.getAmount().compareTo(BigDecimal.ZERO) > 0);
```

### 错误处理测试
```java
// 验证异常抛出
assertThrowsWithMessage(
    ValidationException.class,
    "债权金额不能为负数",
    () -> debtService.addDebt(invalidRequest)
);
```

## 🔧 故障排除

### 常见问题解决
1. **快照不匹配**: 查看 `target/snapshot-diffs/` 差异报告
2. **测试数据污染**: 确保使用 `@Transactional` 和 `@Rollback`
3. **并发测试失败**: 使用独立命名空间隔离数据
4. **H2兼容性问题**: 查看 `H2Configuration` 函数映射

### 调试技巧
```bash
# 启用详细日志
mvn test -Dlogging.level.com.financial=DEBUG

# 单独运行失败的测试
mvn test -Dtest=SpecificFailingTest

# 跳过测试进行快速构建
mvn clean install -DskipTests
```

## 📚 相关文档

### 测试策略文档
- [测试策略总览](../docs/testing/test-strategy.md)
- [数据一致性测试指南](../docs/testing/data-consistency-testing.md)
- [测试实施指南](../docs/testing/test-implementation-guide.md)
- [测试数据准备指南](../docs/testing/test-data-setup.md)

### 框架使用指南
- [测试框架README](../test-framework/README.md)
- [快照管理脚本](../scripts/manage-snapshots.sh)
- [测试基类使用说明](../test-framework/test-core/src/main/java/com/financial/test/base/)

## 交付物

**测试文件**: 在适当测试目录中创建 | 遵循命名约定 | 全面测试用例

**快照文件**: `test-data/snapshots/` 中的JSON快照 | 自动版本管理 | 差异报告生成

**覆盖率报告**: `target/site/jacoco/` 中的HTML报告 | 控制台摘要 | 未覆盖行识别

**一致性报告**: 跨表数据一致性检查报告 | 快照对比差异报告 | 数据质量评估

**CI配置**: GitHub Actions完整配置 | 自动化测试流水线 | 质量门禁设置

**文档**: 测试使用手册 | 故障排除指南 | 最佳实践说明

@include shared/universal-constants.yml#Standard_Messages_Templates