# /init - Initialize Project Command

Initialize a new project with Claude Code configuration.

## Usage
```
/init [project-type]
```

## Parameters
- `project-type` (optional) - Type of project to initialize (e.g., react, python, java)

## Description
Sets up a new project with appropriate Claude Code configuration files, including:
- `.claude/settings.json` - Basic Claude configuration
- `.claude/commands/` - Command definitions
- Project-specific configuration based on detected or specified project type

## Examples
- `/init` - Initialize with auto-detected project type
- `/init react` - Initialize as React project
- `/init python` - Initialize as Python project

## See Also
- `/COMMANDS` - List all available commands