# cleanup-temp-files

**简要描述**  
清理项目中无用的临时文件、缓存文件以及修复过程中产生的中间产物；对于可能包含重要信息的日志或报告，自动归档到指定文档目录。

## Usage  
## Arguments
- `--days=<n>`  
  保留最近 n 天内修改过的临时文件；删除更早的文件。默认 `2`。
- `--archive-path=<path>`  
  将发现的重要日志、错误报告、堆栈跟踪等归档至此目录。默认 `.docs/archive`。

## Instructions
1. **扫描常见临时目录**
    - 查找项目中常见的临时或缓存目录，如：
        - `build/`、`dist/`、`target/`
        - `node_modules/.cache/`、`.tmp/`、`*.log`
        - IDE 产生的隐藏文件（如 `.idea/`, `*.iml`）
    - 可通过 `$ARGUMENTS` 中的 `--days` 参数过滤文件修改时间。
    - 项目根目录中产生的临时性质的md、bak格式的文档，永远不要删除CLAUDE.md文件

2. **检测并归档重要文件**
    - 对于误删后难以恢复的日志或报告（后缀 `.log`, `.err`, `.diag` 等），在删除前：
        1. 将其移动到 `--archive-path` 指定的归档目录。
        2. 如果该目录中已有同名文件，自动重命名为 `<filename>_<YYYYMMDD_HHMMSS>.<ext>`。
    - 在归档完成后，更新项目文档（如 `docs/CHANGELOG.md` 或 `docs/cleanup-notes.md`），记录归档内容与时间。

3. **删除无用文件**
    - 删除所有未被归档、且符合过滤条件的临时文件与目录。
    - 针对空目录，也一并清理。

4. **验证项目完整性**
    - 执行项目自带的清理命令：
      ```bash
      make clean    # 或 mvn clean / npm run clean 等
      ```  
    - 确保格式化、编译和测试不受影响：
      ```bash
      make fmt && make lint && make test
      ```

5. **报告与总结**
    - 在命令执行完成后，输出以下汇总：
        - 归档文件列表
        - 删除文件列表
    - 若存在异常或失败，请暂停并按照“研究→规划→实施”流程进行排查。

## 示例  
> **提示**
> - 建议将此命令定期运行（如每周一次），可配合 CI/CD 定时触发。
> - 如需保留更多类型的文件，可在命令中扩展过滤规则或在项目根目录添加 `.cleanupignore` 文件，列出排除规则。