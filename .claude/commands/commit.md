# Claude 命令：提交代码

该命令帮助您创建格式良好的提交，使用约定式提交消息和表情符号。

## 使用方法

创建提交，只需输入：
```
/commit
```

或使用选项：
```
/commit --no-verify
```

## 该命令的功能

1. 除非使用 `--no-verify` 指定，否则自动运行预提交检查：
   - `mvn clean compile` 确保代码编译成功
   - `mvn test` 运行测试验证
   - `npm run lint` 确保前端代码质量（如果存在）
   - `npm run quality:check` 运行完整的代码质量检查
   - 验证数据库连接和迁移脚本
   - `mvn spring-boot:run -Dspring-boot.run.arguments=--spring.profiles.active=test` 验证后端应用启动
   - `npm start` 验证前端应用启动（如果存在）
   - 检查关键API端点健康状态 `/actuator/health`
2. 使用 `git status` 检查哪些文件已暂存
3. 如果没有文件暂存，使用 `git add` 自动添加所有修改和新增文件
4. 执行 `git diff` 以了解正在提交的更改
5. 分析差异以确定是否存在多个不同的逻辑更改
6. 如果检测到多个不同的更改，建议将提交拆分为多个较小的提交
7. 对于每个提交（或单个提交，如果不拆分），使用表情符号约定式提交格式创建提交消息

## 提交最佳实践

- **提交前验证**：确保代码已lint、构建正确、文档已更新
- **原子提交**：每个提交应包含服务于单一目的的相关更改
- **拆分大型更改**：如果更改涉及多个关注点，将其拆分为单独的提交
- **约定式提交格式**：使用格式 `<类型>: <描述>`，其中类型为以下之一：
  - `feat`: 新功能
  - `fix`: 错误修复
  - `docs`: 文档更改
  - `style`: 代码样式更改（格式等）
  - `refactor`: 既不修复错误也不添加功能的代码更改
  - `perf`: 性能改进
  - `test`: 添加或修复测试
  - `chore`: 构建过程、工具等的更改
- **现在时，祈使语气**：将提交消息写成命令（例如"add feature"而不是"added feature"）
- **简洁的第一行**：保持第一行在72个字符以内
- **表情符号**：每种提交类型都配有适当的表情符号：
  - ✨ `feat`: 新功能
  - 🐛 `fix`: 错误修复
  - 📝 `docs`: 文档
  - 💄 `style`: 格式/样式
  - ♻️ `refactor`: 代码重构
  - ⚡️ `perf`: 性能改进
  - ✅ `test`: 测试
  - 🔧 `chore`: 工具、配置
  - 🚀 `ci`: CI/CD改进
  - 🗑️ `revert`: 撤销更改
  - 🧪 `test`: 添加失败测试
  - 🚨 `fix`: 修复编译器/linter警告
  - 🔒️ `fix`: 修复安全问题
  - 👥 `chore`: 添加或更新贡献者
  - 🚚 `refactor`: 移动或重命名资源
  - 🏗️ `refactor`: 进行架构更改
  - 🔀 `chore`: 合并分支
  - 📦️ `chore`: 添加或更新编译文件或包
  - ➕ `chore`: 添加依赖项
  - ➖ `chore`: 删除依赖项
  - 🌱 `chore`: 添加或更新种子文件
  - 🧑‍💻 `chore`: 改善开发者体验
  - 🧵 `feat`: 添加或更新与多线程或并发相关的代码
  - 🔍️ `feat`: 改进SEO
  - 🏷️ `feat`: 添加或更新类型
  - 💬 `feat`: 添加或更新文本和字面量
  - 🌐 `feat`: 国际化和本地化
  - 👔 `feat`: 添加或更新业务逻辑
  - 📱 `feat`: 响应式设计工作
  - 🚸 `feat`: 改善用户体验/可用性
  - 🩹 `fix`: 非关键问题的简单修复
  - 🥅 `fix`: 捕获错误
  - 👽️ `fix`: 由于外部API更改而更新代码
  - 🔥 `fix`: 删除代码或文件
  - 🎨 `style`: 改进代码结构/格式
  - 🚑️ `fix`: 关键热修复
  - 🎉 `chore`: 开始项目
  - 🔖 `chore`: 发布/版本标签
  - 🚧 `wip`: 正在进行的工作
  - 💚 `fix`: 修复CI构建
  - 📌 `chore`: 将依赖项固定到特定版本
  - 👷 `ci`: 添加或更新CI构建系统
  - 📈 `feat`: 添加或更新分析或跟踪代码
  - ✏️ `fix`: 修复拼写错误
  - ⏪️ `revert`: 撤销更改
  - 📄 `chore`: 添加或更新许可证
  - 💥 `feat`: 引入破坏性更改
  - 🍱 `assets`: 添加或更新资源
  - ♿️ `feat`: 改善可访问性
  - 💡 `docs`: 在源代码中添加或更新注释
  - 🗃️ `db`: 执行数据库相关更改
  - 🔊 `feat`: 添加或更新日志
  - 🔇 `fix`: 删除日志
  - 🤡 `test`: 模拟事物
  - 🥚 `feat`: 添加或更新彩蛋
  - 🙈 `chore`: 添加或更新.gitignore文件
  - 📸 `test`: 添加或更新快照
  - ⚗️ `experiment`: 进行实验
  - 🚩 `feat`: 添加、更新或删除功能标志
  - 💫 `ui`: 添加或更新动画和过渡
  - ⚰️ `refactor`: 删除死代码
  - 🦺 `feat`: 添加或更新与验证相关的代码
  - ✈️ `feat`: 改善离线支持

## 拆分提交的指导原则

在分析差异时，考虑基于以下标准拆分提交：

1. **不同关注点**：对代码库不相关部分的更改
2. **不同类型的更改**：混合功能、修复、重构等
3. **文件模式**：对不同类型文件的更改（例如源代码与文档）
4. **逻辑分组**：单独理解或审查会更容易的更改
5. **大小**：如果分解会更清晰的非常大的更改

## 金融系统特定示例

适合金融系统的良好提交消息：
- ✨ feat: 添加债权风险评估系统
- 🐛 fix: 修复债权金额计算的精度问题
- 📝 docs: 更新API文档，增加债权管理端点
- ♻️ refactor: 简化债权处置流程的错误处理逻辑
- 🚨 fix: 解决债权查询组件中的linter警告
- 🧑‍💻 chore: 改进金融系统开发工具设置流程
- 👔 feat: 实现债权交易验证的业务逻辑
- 🩹 fix: 修复债权列表页面的小样式不一致问题
- 🚑️ fix: 修补认证流程中的关键安全漏洞
- 🎨 style: 重新组织债权管理组件结构以提高可读性
- 🔥 fix: 删除已弃用的旧版债权处理代码
- 🦺 feat: 为债权数据输入添加验证功能
- 💚 fix: 解决CI流水线中的Maven测试失败
- 📈 feat: 实现债权管理的用户操作分析跟踪
- 🔒️ fix: 加强债权数据访问的权限要求
- ♿️ feat: 改善债权表单的屏幕阅读器可访问性
- 🗃️ db: 添加债权历史记录表及相关索引
- 🌐 feat: 为债权管理系统添加多语言支持
- 🔊 feat: 添加债权处置操作的审计日志
- 📦️ chore: 更新Spring Boot版本到3.1.12
- 👷 ci: 添加债权数据一致性检查到CI流程
- 🐳 chore: 优化Docker镜像构建和本地缓存
- 🌍 db: 修复中文数据库名兼容性问题
- 🔧 fix: 优化多数据源配置和连接池
- 🔐 feat: 实现JWT token自动刷新机制
- 📊 feat: 添加债权统计图表和可视化组件
- 🚀 ci: 完善自动部署和回滚机制
- 🔄 feat: 实现数据同步和一致性检查
- 🏢 feat: 添加公司权限管理和层级控制
- 💼 feat: 集成金蝶ERP系统数据同步

金融系统拆分提交的示例：
- 第一次提交：✨ feat: 添加新的债权类型定义
- 第二次提交：📝 docs: 更新债权类型的API文档
- 第三次提交：🔧 chore: 更新pom.xml中的依赖项
- 第四次提交：🏷️ feat: 为新债权API端点添加类型定义
- 第五次提交：🧵 feat: 改进债权处理工作线程的并发处理
- 第六次提交：🚨 fix: 解决新代码中的linting问题
- 第七次提交：✅ test: 为新债权类型功能添加单元测试
- 第八次提交：🔒️ fix: 更新有安全漏洞的依赖项
- 第九次提交：🗃️ db: 执行债权表结构迁移
- 第十次提交：💡 docs: 在债权业务逻辑中添加详细注释

## 命令选项

- `--no-verify`: 跳过预提交检查（lint、build、test）

## 重要说明

- 默认情况下，会运行预提交检查（`mvn compile`、`mvn test`、前端lint等）以确保代码质量
- 如果这些检查失败，会询问您是否要继续提交或先修复问题
- 如果特定文件已经暂存，命令将只提交这些文件
- 如果没有文件暂存，会自动暂存所有修改和新增文件
- 提交消息将基于检测到的更改构建
- 在提交前，命令会检查差异以确定是否更适合多次提交
- 如果建议多次提交，会帮助您分别暂存和提交更改
- 始终检查提交差异以确保消息与更改匹配
- 特别关注金融数据的敏感性，确保不提交敏感信息如密码、密钥等
- 对于数据库迁移相关的提交，使用 🗃️ `db` 类型
- 对于业务逻辑更改，使用 👔 `feat` 类型
- 对于安全相关修复，使用 🔒️ `fix` 类型
- 对于Docker相关改动，使用 🐳 `chore` 类型
- 对于多数据源配置，使用 🔧 `fix` 类型
- 对于CI/CD流程改进，使用 🚀 `ci` 类型
- 对于数据同步相关，使用 🔄 `feat` 类型
- 对于权限管理相关，使用 🏢 `feat` 类型
- 对于外部系统集成，使用 💼 `feat` 类型
- 对于图表和可视化，使用 📊 `feat` 类型
- 遵循SuperClaude的核心原则：研究 → 规划 → 实现
- 提交前确保通过所有质量检查和测试