# 添加认证系统

实现强大的认证和授权系统，采用安全最佳实践和现代化标准，专为金融系统设计。

## 指令说明

1. **认证策略分析**
   - 分析应用需求和用户类型（管理员、普通用户、审计员）
   - 定义认证方法（密码、OAuth、SSO、MFA）
   - 评估安全要求和合规性需求（金融行业标准）
   - 规划用户管理和基于角色的访问控制（RBAC）
   - 评估现有认证基础设施和集成点

2. **认证方法选择**
   - 选择适当的认证策略：
     - **用户名/密码**: 传统的基于凭证的认证
     - **OAuth 2.0/OpenID Connect**: 第三方认证（Google、GitHub等）
     - **SAML**: 企业单点登录集成
     - **JWT**: 无状态基于令牌的认证（当前系统采用）
     - **多因素认证**: SMS、TOTP、硬件令牌
     - **无密码认证**: 魔法链接、WebAuthn、生物识别

3. **用户管理系统**
   - 设置用户注册和账户创建工作流
   - 配置用户档案管理和数据存储
   - 实现密码策略和安全要求
   - 设置账户验证和邮箱确认
   - 配置用户停用和账户删除程序

4. **认证实现**
   - 实现安全密码哈希（bcrypt、Argon2、scrypt）
   - 设置会话管理和令牌生成
   - 配置安全Cookie处理和CSRF保护
   - 实现认证中间件和路由保护
   - 设置认证状态管理（客户端）

5. **授权和访问控制**
   - 实现基于角色的访问控制（RBAC）系统
   - 设置基于权限的授权
   - 配置资源级访问控制
   - 实现动态授权和策略引擎
   - 设置API端点保护和授权

6. **多因素认证（MFA）**
   - 配置基于TOTP的认证应用支持
   - 设置基于SMS的认证码
   - 实现备份码和恢复机制
   - 配置硬件令牌支持（FIDO2/WebAuthn）
   - 设置MFA强制政策和用户体验

7. **OAuth和第三方集成**
   - 配置OAuth提供商（Google、GitHub、Facebook等）
   - 设置OpenID Connect身份联盟
   - 实现社交登录和账户链接
   - 配置企业SSO集成（SAML、LDAP）
   - 设置外部集成的API密钥管理

8. **安全实现**
   - 配置速率限制和暴力破解保护
   - 设置账户锁定和安全监控
   - 实现安全头部和会话安全
   - 配置审计日志和安全事件跟踪
   - 设置漏洞扫描和安全测试

9. **用户体验和前端集成**
   - 创建响应式认证UI组件
   - 实现客户端认证状态管理
   - 设置受保护的路由处理和重定向
   - 配置认证错误处理和用户反馈
   - 实现记住我和持久登录功能

10. **测试和维护**
    - 设置全面的认证测试
    - 配置安全测试和渗透测试
    - 创建认证监控和告警
    - 设置合规报告和审计跟踪
    - 培训团队认证安全最佳实践
    - 创建安全事件的事件响应程序

## 金融系统特定配置

### 当前系统集成点
- **Spring Boot 3.1.12**: 使用Spring Security配置
- **JWT认证**: 已实现的24小时令牌过期机制
- **多数据库**: 用户系统库、逾期债权库、金蝶库的安全隔离
- **角色权限**: ADMIN、USER、VIEWER三级权限体系

### 推荐实现步骤
1. 增强现有JWT认证（添加刷新令牌机制）
2. 实现多因素认证（特别是管理员操作）
3. 设置基于公司的数据访问控制
4. 配置金融级审计日志
5. 实现API速率限制和监控
6. 设置安全事件自动告警

### 合规性要求
- 密码复杂度策略
- 会话超时管理
- 数据访问审计
- 用户操作日志
- 安全事件记录
- 定期安全评估