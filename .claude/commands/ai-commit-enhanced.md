# AI增强智能提交系统

## 命令描述
AI增强智能提交系统，集成Claude Code hooks和Git hooks，实现从代码生成到自动化测试的完整流程。

## 使用语法
```bash
claude ai-commit-enhanced <message> [options]
```

## 参数说明

### 必需参数
- `message`: 提交描述信息

### 可选参数
- `--auto_fix`: 启用自动修复循环 (默认: true)
- `--quality_threshold`: 质量阈值 0-1 (默认: 0.8)
- `--branch_type`: 分支类型 (feature/hotfix/release)
- `--fast_track`: 快速通道模式 (默认: false)
- `--test_mode`: 测试模式，不实际提交 (默认: false)
- `--dry_run`: 预演模式，显示将要执行的操作

## 执行流程

### 阶段1: PreToolUse - 质量预检
```javascript
// 自动触发Claude hooks
await claudeHooks.PreToolUse.execute({
  workspace: currentWorkspace,
  branch: currentBranch,
  context: userContext
});
```

**检查项目**:
- ✅ 工作区状态检查
- ✅ 分支策略验证
- ✅ 依赖完整性检查
- ✅ 质量基线捕获
- ✅ 环境配置验证

### 阶段2: 代码分析和AI处理
```javascript
// AI智能分析和代码处理
const analysis = await ai.analyzeCodebase({
  changes: detectedChanges,
  context: commitContext,
  requirements: userRequirements
});

const modifications = await ai.processCode(analysis);
```

**AI处理流程**:
- 🔍 变更影响分析
- 🧠 代码逻辑优化
- 🔒 安全漏洞检测
- 📊 性能影响评估
- 📝 文档自动更新

### 阶段3: PostToolUse - 结果验证
```javascript
// 验证AI处理结果
await claudeHooks.PostToolUse.execute({
  changes: modifications,
  analysis: analysis,
  baseline: qualityBaseline
});
```

**验证检查项**:
- ✅ 语法正确性检查
- ✅ 类型系统验证
- ✅ 单元测试执行
- ✅ 代码覆盖率检查
- ✅ 安全扫描验证

### 阶段4: Notification - Git集成桥接
```javascript
// 触发Git hooks链
await claudeHooks.Notification.execute({
  notification: 'code_completed',
  metadata: {
    tool: currentTool,
    changes: modifications,
    context: commitContext
  }
});
```

**桥接操作**:
- 📦 自动暂存变更 (`git add .`)
- 💬 智能生成提交信息
- 🚀 触发Git提交流程
- 🔗 启动Git hooks链

### 阶段5: Git Hooks自动化循环
```bash
# Git pre-commit hook 执行
.git/hooks/pre-commit
├── 代码格式化 (prettier/eslint)
├── 类型检查 (TypeScript/Flow)
├── 单元测试执行
├── 安全扫描 (audit)
└── 覆盖率验证

# Git post-commit hook 执行  
.git/hooks/post-commit
├── 集成测试触发
├── 性能回归测试
├── 构建验证
└── 部署流水线触发
```

### 阶段6: 智能修复循环
```python
# 自动修复循环逻辑
max_attempts = 3
current_attempt = 0

while current_attempt < max_attempts:
    test_results = run_all_tests()
    
    if test_results.all_passed():
        promote_to_stable()
        break
    
    # AI分析失败原因并生成修复方案
    failure_analysis = ai.analyze_failures(test_results.failures)
    fixes = ai.generate_fixes(failure_analysis)
    
    # 应用修复
    apply_fixes(fixes)
    current_attempt += 1

if current_attempt >= max_attempts:
    escalate_to_human()
```

### 阶段7: Stop - 最终质量门控
```javascript
// 最终质量评估和报告
await claudeHooks.Stop.execute({
  overallResult: pipelineResult,
  metrics: qualityMetrics,
  decisions: automatedDecisions
});
```

**最终检查项**:
- 📊 综合质量评分
- 🎯 业务逻辑验证
- 🔍 回归测试确认
- 📈 性能基准对比
- 📋 完整性报告生成

## 使用示例

### 基础用法
```bash
# 简单AI提交
claude ai-commit-enhanced "实现用户权限管理功能"

# 指定分支类型
claude ai-commit-enhanced "添加支付接口" --branch_type=feature

# 高质量要求
claude ai-commit-enhanced "核心安全模块重构" --quality_threshold=0.95
```

### 高级用法
```bash
# 紧急修复（快速通道）
claude ai-commit-enhanced "修复登录安全漏洞" \\
  --branch_type=hotfix \\
  --fast_track=true \\
  --auto_fix=true

# 测试模式（不实际提交）
claude ai-commit-enhanced "测试新功能" \\
  --test_mode=true \\
  --dry_run=true

# 禁用自动修复
claude ai-commit-enhanced "手动控制的提交" \\
  --auto_fix=false \\
  --quality_threshold=0.6
```

### 配置文件示例
```json
// .claude/config/ai-commit.json
{
  "defaults": {
    "auto_fix": true,
    "quality_threshold": 0.8,
    "max_fix_attempts": 3
  },
  "branch_policies": {
    "main": {
      "quality_threshold": 0.95,
      "require_human_review": true,
      "auto_fix": false
    },
    "develop": {
      "quality_threshold": 0.8,
      "auto_fix": true
    },
    "feature/*": {
      "quality_threshold": 0.7,
      "auto_fix": true,
      "fast_track_allowed": true
    }
  },
  "notifications": {
    "slack_webhook": "https://hooks.slack.com/...",
    "email_alerts": ["<EMAIL>"],
    "success_notification": true,
    "failure_notification": true
  }
}
```

## 错误处理

### 常见错误及解决方案

**1. 分支保护错误**
```
❌ 错误: 当前分支 "main" 不允许AI直接修改
🔧 解决: 切换到 feature 分支或使用 --force 参数
```

**2. 质量检查失败**
```
❌ 错误: 代码覆盖率 65% 低于阈值 80%
🔧 解决: 自动修复循环已启动，正在生成测试用例...
```

**3. 依赖冲突**
```
❌ 错误: NPM依赖解析失败
🔧 解决: 正在执行 npm install 自动修复...
```

**4. Git冲突**
```
❌ 错误: 提交时发现合并冲突
🔧 解决: 启动AI冲突解决器...
```

## 监控和报告

### 实时状态监控
```bash
# 查看AI提交状态
claude status --ai-commit

# 查看质量报告
claude report --quality --last=10

# 查看失败分析
claude report --failures --detailed
```

### 质量指标仪表板
- 📊 提交成功率趋势
- 🎯 质量分数分布
- 🔧 自动修复效率
- ⏱️ 平均处理时间
- 🚨 失败原因分析

## 集成配置

### SuperClaude配置
```yaml
# .claude/settings.json
{
  "ai_commit_enhanced": {
    "enabled": true,
    "hooks_integration": true,
    "auto_mode": {
      "trigger": "code_completion",
      "quality_gate": true,
      "fix_loop": true
    }
  }
}
```

### Git Hooks配置
```bash
# 安装增强Git hooks
cp .claude/hooks/git-integration/* .git/hooks/
chmod +x .git/hooks/*

# 配置环境变量
echo "CLAUDE_AI_GITFLOW=enabled" >> .env
echo "AI_COMMIT_ENHANCED=true" >> .env
```

## 最佳实践

### 1. 分支策略
- ✅ 在 `feature/*` 分支使用AI提交
- ✅ `develop` 分支进行集成测试
- ⚠️ `main` 分支需要人工审查
- 🚫 禁止在 `release/*` 分支直接AI提交

### 2. 质量控制
- 设置合理的质量阈值（建议0.8）
- 启用自动修复循环
- 定期审查AI生成的代码
- 保持测试覆盖率 > 80%

### 3. 团队协作
- 使用统一的提交信息模板
- 配置团队通知渠道
- 建立人工介入机制
- 定期分析AI提交质量

### 4. 性能优化
- 使用并行测试执行
- 缓存依赖检查结果
- 优化hooks执行顺序
- 监控系统资源使用

---

*AI-Commit-Enhanced v1.0 | Claude Code + Git 深度集成 | 企业级AI开发解决方案*