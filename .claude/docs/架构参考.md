# 架构参考

## 🏗️ 核心技术栈
- **后端**: Spring Boot 3.1.12 + Java 21，JWT认证，多数据源JPA
- **前端**: React 18.2.0 + Material-UI v5.15.20，Chart.js/Recharts可视化
- **数据库**: 三个MySQL 8.0数据库（overdue_debt_db，user_system，kingdee）
- **构建工具**: 后端Maven（多模块），前端npm
- **部署**: Docker Compose + Nginx反向代理
- **CI/CD**: 基于Git推送的自动部署，支持回滚功能

## 📁 模块结构
```
FinancialSystem/
├── api-gateway/              # 主API网关和控制器
├── services/                 # 业务服务模块
│   ├── debt-management/      # 核心债权管理逻辑
│   ├── account-management/   # 用户和认证服务
│   ├── audit-management/     # 审计和日志服务
│   └── report-management/    # 报表生成服务
├── shared/                   # 共享组件
│   ├── common/              # 实体、DTO、工具类
│   ├── data-access/         # 仓储层和数据配置
│   ├── data-processing/     # 数据处理服务
│   └── report-core/         # 报表生成核心
├── integrations/            # 外部系统集成
│   ├── treasury/            # 银行国库系统集成
│   ├── oa-workflow/         # OA工作流系统集成
│   └── kingdee/             # 金蝶ERP集成
├── FinancialSystem-web/     # React前端应用
└── docs/                    # 综合文档
```