# 数据库架构

## 💾 主数据库（overdue_debt_db）
- **新增表 (OverdueDebtAdd)**: 主要债权记录，使用复合主键
- **处置表 (OverdueDebtDecrease)**: 债权处置/减免记录
- **减值准备表 (ImpairmentReserve)**: 减值准备数据
- **诉讼表 (LitigationClaim)**: 诉讼债权
- **非诉讼表 (NonLitigationClaim)**: 非诉讼债权

## 🔍 元数据详情
- **数据库中存在中文数据库名，库名为overdue_debt_db** - 确认存在中文数据库名称

## 👥 用户系统数据库
- **User**: 用户身份验证和配置文件
- **Role**: 角色定义和权限
- **Company**: 公司结构和层级关系
- **UserCompanyPermission**: 用户-公司权限映射

## 🏢 金蝶数据库
- 与金蝶ERP系统的只读集成
- 用于报表的同步财务数据