# 部署指南

## 🚀 生产环境
- **服务器**: Linux生产服务器 (10.25.1.85)
- **路径**: `/opt/FinancialSystem/current`
- **端口**: 前端(80)，后端(8080)，数据库(3306)
- **备份**: 30天保留策略

## 🔄 CI/CD流水线特性
- **自动部署**: 由Git推送到主分支触发
- **智能回滚**: 系统具备部署历史跟踪
- **全面健康检查**: API、数据库、前端检查
- **自动恢复机制**: 针对常见问题的自动恢复
- **Docker镜像优化**: 本地缓存机制
- **多环境支持**: 开发/生产环境配置

## 🐳 Docker服务
- **MySQL 8.0**: 带健康检查的数据库
- **后端**: Spring Boot应用 (OpenJDK 21)
- **前端**: React应用 (Node.js 18)
- **Nginx**: Web服务器和反向代理
- **所有Docker镜像都在本地构建和使用，避免从远程仓库拉取，提高部署速度和稳定性**

## 🔧 核心配置文件
- **后端配置**: `api-gateway/src/main/resources/application.yml`
- **前端配置**: `FinancialSystem-web/package.json`
- **Docker配置**: `docker-compose.yml`, `docker-compose.local.yml`
- **CI/CD配置**: `.ci-cd-config`, `ci-cd/` 目录
- **数据库配置**: Spring Boot中的多数据源配置
- **Nginx配置**: `config/nginx/nginx.conf`

## 🖥️ 环境说明
- 在本地是直接启动，不用使用docker启动，在linux中使用docker启动
- 记住我的linux系统中的是使用docker运行代码，docker镜像使用的是本地的镜像