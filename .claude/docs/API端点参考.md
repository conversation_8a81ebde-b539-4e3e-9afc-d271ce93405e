# API端点参考

## 🔌 认证接口 (`/api/auth`)
- `POST /api/auth/login` - 用户身份验证，生成JWT令牌
- `POST /api/auth/reset-password` - 密码重置功能
- `GET /api/protected` - 受保护资源访问验证

## 💰 债权管理接口 (`/api/debts`)
- `POST /api/debts/add` - 添加新的逾期债权记录
- `POST /api/debts/update/reduction` - 更新债权处置/减免数据
- `DELETE /api/debts/delete/disposal` - 删除处置记录
- `GET /api/debts/statistics` - 带筛选条件的债权统计
- `GET /api/debts/search` - 按债权人/债务人搜索债权记录

## 📊 数据导出接口 (`/api/export`)
- `GET /api/export/complete-report` - 导出综合报表
- Excel报表生成使用Apache POI和Aspose.Cells

## 🔍 系统监控接口
- `GET /actuator/health` - 系统健康检查
- `GET /api/datamonitor/consistency` - 数据一致性检查