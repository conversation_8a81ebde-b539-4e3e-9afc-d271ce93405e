# 开发指南

## 🛠️ 常用命令

### 后端开发
```bash
# 构建所有模块
mvn clean package

# 运行测试
mvn test

# 运行特定模块
mvn spring-boot:run -pl api-gateway
```

### 前端开发
```bash
# 安装依赖
npm install

# 启动开发服务器
npm start

# 生产环境构建
npm run build

# 运行代码质量检查
npm run quality:check
```

### Docker操作
```bash
# 构建并启动服务
docker-compose up -d

# 使用本地镜像（开发环境）
docker-compose -f docker-compose.local.yml up -d

# 查看日志
docker-compose logs -f

# 健康检查
curl http://localhost:8080/actuator/health
```

## 🖥️ 开发环境
- **本地开发**: 使用 `docker-compose.local.yml` 优先使用本地镜像
- **构建命令**: 后端 `mvn clean package`，前端 `npm run build`
- **代码质量**: 已配置ESLint、Prettier和类型检查

## 📊 前端架构

### React应用结构
- **Material-UI v5**: 自定义MD前缀组件
- **Chart.js**: 数据可视化，自定义配置
- **React Router v6**: 客户端路由，受保护路由
- **Context API**: 认证和UI的全局状态管理
- **Axios**: HTTP客户端，带认证和错误处理拦截器

### 核心组件
- **DashboardLayout**: 主应用布局，响应式侧边栏
- **GenericDataTable**: 可复用表格组件，带分页
- **DebtStatisticsChart**: Chart.js包装器，用于债权分析
- **认证表单**: 带验证的登录/注册表单