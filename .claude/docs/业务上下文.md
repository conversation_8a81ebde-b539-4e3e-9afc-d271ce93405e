# 业务上下文参考

## 🎯 核心业务功能
1. **债权生命周期管理** - 从记录到处置的完整流程
2. **多维度报表分析** - 公司、时间、类别等维度分析
3. **数据完整性保障** - 跨表验证和一致性检查
4. **灵活的导出功能** - Excel报表，支持自定义筛选
5. **基于角色的安全控制** - 细粒度权限管理
6. **审计追踪** - 完整的交易历史和变更跟踪
7. **集成就绪** - 外部系统连接的API

## 🏢 核心业务实体
- **债权人 (Creditor)** - 被欠款的实体
- **债务人 (Debtor)** - 欠款的实体
- **逾期债权 (Overdue Debt)** - 逾期的金融债务
- **处置 (Disposal)** - 解决债权采取的行动
- **减值准备 (Impairment Reserve)** - 坏账的会计准备
- **诉讼/非诉讼 (Litigation/Non-litigation)** - 法律vs非法律债权追讨

## 🔒 安全与认证

### 认证功能
- **基于JWT的认证** - 24小时令牌有效期
- **基于角色的访问控制** - (ADMIN, USER, VIEWER)
- **多数据源安全** - 适当的用户隔离
- **CORS配置** - 由Spring Security处理

### 授权级别
- **公共访问**: 登录、健康检查、Swagger文档
- **认证用户**: 基本债权管理操作
- **管理员用户**: 用户管理、系统监控、审计日志
- **导出用户**: 数据导出功能

## 📚 文档资源

### 核心文档文件
- **API文档**: `/docs/api/README.md` - 完整的API接口文档
- **部署指南**: `/docs/deployment/README.md` - 部署和CI/CD设置
- **故障排除**: `/docs/troubleshooting/README.md` - 常见问题和解决方案
- **开发指南**: `/docs/development/README.md` - 开发环境设置
- **业务逻辑**: `/docs/business/` - 业务需求和逻辑文档

### 综合文档结构
- **60+文档文件** 涵盖系统的各个方面
- **双语文档** (中英文) 用于业务需求
- **架构图表** 和技术规范
- **集成指南** 用于外部系统 (OA、Treasury、Kingdee)
- **运维手册** 用于生产管理

## 📖 系统总结
本系统处理复杂的金融关系，具有完善的业务规则、全面的报表功能，以及适用于金融机构的企业级安全性。