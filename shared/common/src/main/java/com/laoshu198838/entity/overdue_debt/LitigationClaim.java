package com.laoshu198838.entity.overdue_debt;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;


/**
 * 诉讼债权表实体类
 * 对应数据库中的诉讼债权表
 * <AUTHOR>
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@Getter
@Setter
@Entity
@Table(name = "诉讼表")
public class LitigationClaim {

    @EmbeddedId
    private LitigationCompositeKey id;

    @Column(name = "序号", nullable = false)
    private int sequence;

    @Column(name = "诉讼案件", length = 100)
    private String litigationCase;

    @Column(name = "管理公司", length = 30)
    private String managementCompany;

    @Column(name = "科目名称", length = 30)
    private String subjectName;

    @Column(name = "上月末债权余额")
    private BigDecimal lastMonthDebtBalance;

    @Column(name = "涉诉债权本金")
    private BigDecimal litigationPrincipal;

    @Column(name = "涉诉债权应收利息罚息服务费")
    private BigDecimal litigationInterest;

    @Column(name = "本月末债权余额")
    private BigDecimal currentMonthDebtBalance;

    @Column(name = "诉讼主张本金")
    private BigDecimal litigationOccurredPrincipal;

    @Column(name = "诉讼主张应收利息及罚金")
    private BigDecimal litigationInterestFee;

    @Column(name = "诉讼费")
    private BigDecimal litigationFee;

    @Column(name = "中介费")
    private BigDecimal intermediaryFee;

    @Column(name = "终审判决仲裁裁决调解和解金额")
    private BigDecimal finalJudgmentAmount;

    @Column(name = "申请执行金额")
    private BigDecimal executionAmount;

    @Column(name = "实际执行回款")
    private BigDecimal actualExecutionAmount;

    @Column(name = "实际支付费用")
    private BigDecimal actualPaymentFee;

    @Column(name = "本年度回收目标", length = 30)
    private String annualRecoveryTarget;

    @Column(name = "本年度累计回收")
    private BigDecimal annualCumulativeRecovery;

    @Column(name = "安排措施", length = 190)
    private String arrangement;

    @Column(name = "责任人", length = 30)
    private String responsiblePerson;

    @Column(name = "备注", length = 190)
    private String remark;

    @Column(name = "逾期年限", length = 30)
    private String overdueYear;

    @Column(name = "债权类别", length = 30)
    private String claimType;

    @Column(name = "债权性质", length = 30)
    private String debtNature;

    @Column(name = "债权类型", length = 30)
    private String debtType;

    @Column(name = "到期时间")
    private Date dueDate;

    @Column(name = "本月新增债权")
    private BigDecimal currentMonthNewDebt;

    @Column(name = "本月处置债权")
    private BigDecimal currentMonthDisposalDebt;


    /**
     * 诉讼表专用的复合主键类
     * <p>
     * 该类封装“诉讼表”的联合主键字段，
     * 包括债权人、债务人、期间和年份等信息。
     * </p>
     */
    @JsonIgnoreProperties(ignoreUnknown = true)
    @Getter
    @Setter
    @EqualsAndHashCode
    @Embeddable
    public static class LitigationCompositeKey implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        @Column(name = "债权人")
        private String creditor;

        @Column(name = "债务人")
        private String debtor;

        @Column(name = "期间")
        private String period;

        @Column(name = "年份")
        private Integer year;

        @Column(name = "月份")
        private int month;

        // 默认无参构造函数
        public LitigationCompositeKey() {}

    }
}
