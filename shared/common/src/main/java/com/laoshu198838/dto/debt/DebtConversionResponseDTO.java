package com.laoshu198838.dto.debt;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

/**
 * 债权转换响应 DTO
 * 用于返回转换操作的结果信息
 * 
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class DebtConversionResponseDTO {
    
    /**
     * 操作是否成功
     */
    private boolean success;
    
    /**
     * 响应消息
     */
    private String message;
    
    /**
     * 转换类型：litigation_to_non_litigation 或 non_litigation_to_litigation
     */
    private String conversionType;
    
    /**
     * 债权人
     */
    private String creditor;
    
    /**
     * 债务人
     */
    private String debtor;
    
    /**
     * 转换前状态
     */
    private String fromStatus;
    
    /**
     * 转换后状态
     */
    private String toStatus;
    
    /**
     * 操作时间戳
     */
    private Long timestamp;
    
    /**
     * 详细信息
     */
    private String details;
    
    /**
     * 创建成功响应
     */
    public static DebtConversionResponseDTO success(String conversionType, String creditor, String debtor, String message) {
        return DebtConversionResponseDTO.builder()
                .success(true)
                .conversionType(conversionType)
                .creditor(creditor)
                .debtor(debtor)
                .message(message)
                .timestamp(System.currentTimeMillis())
                .build();
    }
    
    /**
     * 创建失败响应
     */
    public static DebtConversionResponseDTO failure(String message, String details) {
        return DebtConversionResponseDTO.builder()
                .success(false)
                .message(message)
                .details(details)
                .timestamp(System.currentTimeMillis())
                .build();
    }
}