package com.laoshu198838.repository.overdue_debt;

import java.util.List;
import java.util.Map;

import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.jpa.repository.QueryHints;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.laoshu198838.annotation.DataSource;
import com.laoshu198838.entity.overdue_debt.OverdueDebtAdd;

import jakarta.persistence.QueryHint;

/**
 * 优化的一致性检查专用仓库
 * 提供跨表一致性检查的SQL查询，包含性能优化
 * 
 * <AUTHOR>
 */
@Repository
@DataSource("primary")
public interface OptimizedConsistencyCheckRepository extends JpaRepository<OverdueDebtAdd, OverdueDebtAdd.OverdueDebtAddKey> {

    /**
     * 获取年初至指定月的累计新增金额汇总（优化版）
     * 使用索引优化和查询提示
     */
    @Query(value = """
            SELECT 
                'yearNewAmount' AS checkType,
                COALESCE(SUM(
                    CASE 
                        WHEN :month >= 1 THEN COALESCE(a.`1月`, 0) ELSE 0 
                    END +
                    CASE 
                        WHEN :month >= 2 THEN COALESCE(a.`2月`, 0) ELSE 0 
                    END +
                    CASE 
                        WHEN :month >= 3 THEN COALESCE(a.`3月`, 0) ELSE 0 
                    END +
                    CASE 
                        WHEN :month >= 4 THEN COALESCE(a.`4月`, 0) ELSE 0 
                    END +
                    CASE 
                        WHEN :month >= 5 THEN COALESCE(a.`5月`, 0) ELSE 0 
                    END +
                    CASE 
                        WHEN :month >= 6 THEN COALESCE(a.`6月`, 0) ELSE 0 
                    END +
                    CASE 
                        WHEN :month >= 7 THEN COALESCE(a.`7月`, 0) ELSE 0 
                    END +
                    CASE 
                        WHEN :month >= 8 THEN COALESCE(a.`8月`, 0) ELSE 0 
                    END +
                    CASE 
                        WHEN :month >= 9 THEN COALESCE(a.`9月`, 0) ELSE 0 
                    END +
                    CASE 
                        WHEN :month >= 10 THEN COALESCE(a.`10月`, 0) ELSE 0 
                    END +
                    CASE 
                        WHEN :month >= 11 THEN COALESCE(a.`11月`, 0) ELSE 0 
                    END +
                    CASE 
                        WHEN :month >= 12 THEN COALESCE(a.`12月`, 0) ELSE 0 
                    END
                ), 0) AS addTableAmount,
                lit.litigationAmount,
                non_lit.nonLitigationAmount,
                imp.impairmentAmount
            FROM `新增表` a
            CROSS JOIN (
                SELECT COALESCE(SUM(l.`本月新增债权`), 0) AS litigationAmount
                FROM `诉讼表` l 
                WHERE l.`年份` = :year AND l.`月份` <= :month
            ) lit
            CROSS JOIN (
                SELECT COALESCE(SUM(n.`本月新增债权`), 0) AS nonLitigationAmount
                FROM `非诉讼表` n 
                WHERE n.`年份` = :year AND n.`月份` <= :month
            ) non_lit
            CROSS JOIN (
                SELECT COALESCE(SUM(i.`本月新增债权`), 0) AS impairmentAmount
                FROM `减值准备表` i 
                WHERE i.`年份` = :year AND i.`月份` <= :month
            ) imp
            WHERE a.`年份` = :year
            """, nativeQuery = true)
    @QueryHints({
        @QueryHint(name = "org.hibernate.cacheable", value = "true"),
        @QueryHint(name = "org.hibernate.cacheMode", value = "NORMAL"),
        @QueryHint(name = "org.hibernate.readOnly", value = "true")
    })
    @Cacheable(value = "yearNewAmountSummary", key = "#year + '_' + #month")
    Map<String, Object> getOptimizedYearToDateNewAmountSummary(@Param("year") int year, @Param("month") int month);

    /**
     * 获取处置金额汇总数据（优化版）
     * 使用JOIN替代子查询提升性能
     */
    @Query(value = """
            SELECT 
                'disposedAmount' as checkType,
                COALESCE(SUM(a.处置金额), 0) as addTableAmount,
                COALESCE(SUM(l.本月处置债权), 0) as litigationAmount,
                COALESCE(SUM(n.本月处置债权), 0) as nonLitigationAmount,
                COALESCE(SUM(i.本月处置债权), 0) as impairmentAmount
            FROM `新增表` a
            LEFT JOIN `诉讼表` l ON l.年份 = :year AND l.月份 = :month
            LEFT JOIN `非诉讼表` n ON n.年份 = :year AND n.月份 = :month  
            LEFT JOIN `减值准备表` i ON i.年份 = :year AND i.月份 = :month
            WHERE a.年份 = :year
            """, nativeQuery = true)
    @QueryHints({
        @QueryHint(name = "org.hibernate.cacheable", value = "true"),
        @QueryHint(name = "org.hibernate.readOnly", value = "true")
    })
    @Cacheable(value = "disposedAmountSummary", key = "#year + '_' + #month")
    Map<String, Object> getOptimizedDisposedAmountSummary(@Param("year") int year, @Param("month") int month);

    /**
     * 分页获取新增金额明细数据（优化版）
     * 支持分页查询，避免大数据量问题
     */
    @Query(value = """
            SELECT 
                a.债务人 as debtor,
                a.债权人 as creditor,
                a.是否涉诉 as isLitigation,
                a.新增金额 as addTableAmount,
                COALESCE(l.本月新增债权, 0) as litigationAmount,
                COALESCE(n.本月新增债权, 0) as nonLitigationAmount,
                COALESCE(i.本月新增债权, 0) as impairmentAmount,
                ABS(a.新增金额 - COALESCE(
                    CASE WHEN a.是否涉诉 = '是' THEN l.本月新增债权 ELSE n.本月新增债权 END, 0
                )) as difference
            FROM `新增表` a
            LEFT JOIN `诉讼表` l ON a.债权人 = l.债权人 AND a.债务人 = l.债务人 
                AND l.年份 = :year AND a.是否涉诉 = '是'
            LEFT JOIN `非诉讼表` n ON a.债权人 = n.债权人 AND a.债务人 = n.债务人 
                AND n.年份 = :year AND a.是否涉诉 = '否'
            LEFT JOIN `减值准备表` i ON a.债权人 = i.债权人 AND a.债务人 = i.债务人 
                AND i.年份 = :year AND a.是否涉诉 = i.是否涉诉
            WHERE a.年份 = :year
            HAVING difference > 0.01
            ORDER BY difference DESC
            """, 
            countQuery = """
            SELECT COUNT(*)
            FROM `新增表` a
            LEFT JOIN `诉讼表` l ON a.债权人 = l.债权人 AND a.债务人 = l.债务人 
                AND l.年份 = :year AND a.是否涉诉 = '是'
            LEFT JOIN `非诉讼表` n ON a.债权人 = n.债权人 AND a.债务人 = n.债务人 
                AND n.年份 = :year AND a.是否涉诉 = '否'
            LEFT JOIN `减值准备表` i ON a.债权人 = i.债权人 AND a.债务人 = i.债务人 
                AND i.年份 = :year AND a.是否涉诉 = i.是否涉诉
            WHERE a.年份 = :year
            AND ABS(a.新增金额 - COALESCE(
                CASE WHEN a.是否涉诉 = '是' THEN l.本月新增债权 ELSE n.本月新增债权 END, 0
            )) > 0.01
            """,
            nativeQuery = true)
    @QueryHints({
        @QueryHint(name = "org.hibernate.readOnly", value = "true")
    })
    Page<Map<String, Object>> findNewAmountDetailWithPagination(@Param("year") int year, Pageable pageable);

    /**
     * 获取期末余额汇总数据（优化版）
     * 使用更高效的JOIN查询
     */
    @Query(value = """
            SELECT 
                'endingBalance' as checkType,
                COALESCE(SUM(a.债权余额), 0) as addTableAmount,
                COALESCE(SUM(l.本月末债权余额), 0) as litigationAmount,
                COALESCE(SUM(n.本月末本金), 0) as nonLitigationAmount,
                COALESCE(SUM(i.本月末债权余额), 0) as impairmentAmount
            FROM `新增表` a
            LEFT JOIN `诉讼表` l ON l.年份 = :year AND l.月份 = :month
            LEFT JOIN `非诉讼表` n ON n.年份 = :year AND n.月份 = :month
            LEFT JOIN `减值准备表` i ON i.年份 = :year AND i.月份 = :month
            WHERE a.年份 = :year
            """, nativeQuery = true)
    @QueryHints({
        @QueryHint(name = "org.hibernate.cacheable", value = "true"),
        @QueryHint(name = "org.hibernate.readOnly", value = "true")
    })
    @Cacheable(value = "endingBalanceSummary", key = "#year + '_' + #month")
    Map<String, Object> getOptimizedEndingBalanceSummary(@Param("year") int year, @Param("month") int month);

    /**
     * 批量获取多个月份的汇总数据（优化版）
     * 一次查询获取多个月份的数据，减少数据库访问次数
     */
    @Query(value = """
            SELECT 
                l.月份 as month,
                'monthlyData' as checkType,
                COALESCE(SUM(l.本月新增债权), 0) as litigationNewAmount,
                COALESCE(SUM(l.本月处置债权), 0) as litigationDisposedAmount,
                COALESCE(SUM(l.本月末债权余额), 0) as litigationEndingBalance
            FROM `诉讼表` l
            WHERE l.年份 = :year AND l.月份 IN :months
            GROUP BY l.月份
            ORDER BY l.月份
            """, nativeQuery = true)
    @QueryHints({
        @QueryHint(name = "org.hibernate.readOnly", value = "true")
    })
    List<Map<String, Object>> getBatchMonthlyLitigationSummary(@Param("year") int year, @Param("months") List<Integer> months);
}
