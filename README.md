# 财务系统 (Financial System)

企业级财务管理系统，基于Spring Boot + React + MySQL架构，支持逾期债权管理、数据分析、报表生成等功能。

🚀 **自动部署功能已启用** - 代码提交到main分支将自动触发部署

## 📋 项目概述

本项目是一个完整的财务管理系统，采用现代化的微服务架构，提供：
- 💰 逾期债权数据管理
- 📊 多维度数据分析
- 📈 可视化报表生成
- 🔄 自动化部署流程
- 🐳 Docker容器化支持

## 🏗️ 技术栈

- **后端**: Spring Boot 3.x, Java 21, JPA, MySQL
- **前端**: React 18, TypeScript, Ant Design
- **数据库**: MySQL 8.0, 多数据源支持
- **容器化**: Docker, Docker Compose
- **构建工具**: Maven, npm
- **CI/CD**: 自动化部署, Webhook服务

## 🚀 快速开始

### 环境要求
- Java 21+
- Node.js 18+
- Docker & Docker Compose
- MySQL 8.0+

### 一键部署
```bash
# 克隆项目
git clone <repository-url>
cd FinancialSystem

# Docker部署 (推荐)
docker-compose up -d

# 访问应用
# 前端: http://localhost
# 后端: http://localhost:8080
# 数据库: localhost:3306
```

### 手动部署
```bash
# 执行部署脚本
./scripts/deploy/deploy.sh

# 检查服务状态
./scripts/deploy/health-check.sh
```

## 📁 项目结构

```
FinancialSystem/
├── 🌐 api-gateway/              # API网关和主应用入口
├── 🎨 FinancialSystem-web/      # 前端React应用
├── 🏢 services/                 # 业务服务模块组
│   ├── debt-management/        # 债务管理服务
│   ├── account-management/     # 账户管理服务
│   ├── audit-management/       # 审计管理服务
│   └── report-management/      # 报表管理服务
├── 🔧 shared/                   # 共享组件模块组
│   ├── common/                 # 公共组件
│   ├── data-access/           # 数据访问层
│   └── data-processing/       # 数据处理组件
├── 🔌 integrations/             # 第三方集成模块组
│   ├── kingdee/               # 金蝶系统集成
│   └── treasury/              # 财政系统集成
├── 📚 docs/                     # 项目文档中心
│   ├── guides/                # 项目指南
│   ├── api/                   # API文档
│   ├── business/              # 业务文档
│   ├── development/           # 开发文档
│   ├── deployment/            # 部署文档
│   ├── operations/            # 运维文档
│   ├── troubleshooting/       # 故障排除
│   ├── planning/              # 项目规划
│   ├── reports/               # 报告文档
│   └── archive/               # 归档文档
├── 📜 scripts/                  # 脚本文件目录
│   ├── build/                 # 构建脚本
│   ├── deploy/                # 部署脚本
│   ├── database/              # 数据库脚本
│   ├── maintenance/           # 维护脚本
│   ├── ci-cd/                 # CI/CD脚本
│   ├── treasury/              # 财政系统脚本
│   ├── ide/                   # IDE相关脚本
│   └── utils/                 # 工具脚本
├── 🚀 ci-cd/                    # CI/CD配置目录
├── ⚙️ config/                   # 配置文件目录
├── 🗄️ sql/                      # 数据库脚本
│   ├── init/                  # 初始化脚本
│   └── migration/             # 迁移脚本
├── 📊 var/                      # 运行时数据目录
│   ├── log/                   # 日志文件
│   └── backups/               # 备份文件
├── 📦 pom.xml                   # Maven主配置文件
├── 🐳 docker-compose.yml        # Docker编排配置
└── 📖 README.md                # 项目说明文档
```

> 📋 详细的目录结构说明请参考: [项目目录结构说明](docs/guides/项目目录结构说明.md)

## 🔧 系统功能

### 核心功能
- ✅ 用户认证与权限管理
- ✅ 逾期债权数据管理
- ✅ 多数据源配置支持
- ✅ 数据导入导出功能
- ✅ 报表生成与分析

### 技术特性
- ✅ 微服务架构设计
- ✅ Docker容器化部署
- ✅ CI/CD自动化流程
- ✅ 数据库迁移管理
- ✅ 健康检查监控

## 📖 文档

- [📚 文档中心](docs/README.md) - 所有文档的入口
- [📋 项目目录结构说明](docs/guides/项目目录结构说明.md) - 详细的目录结构说明
- [📈 项目状态总结](docs/project-status-summary.md) - 项目当前状态和完成度
- [📝 变更日志](docs/CHANGELOG.md) - 项目版本历史和重要变更记录
- [🚀 部署指南](docs/deployment/README.md) - 完整部署流程
- [💻 开发指南](docs/development/README.md) - 开发环境搭建
- [🔧 脚本说明](scripts/README.md) - 自动化脚本使用
- [🛠️ 故障排除](docs/troubleshooting/README.md) - 问题解决方案

## 🎯 系统架构

- **前端服务**: Nginx (端口80)
- **后端API**: Spring Boot (端口8080)
- **数据库**: MySQL 8.0 (端口3306)
- **CI/CD**: Webhook服务 (端口9000)

## 🔐 默认账户

### 主管理员账户
- **用户名**: laoshu198838
- **密码**: Zlb&198838

### 系统管理员账户
- **用户名**: admin
- **密码**: admin123

## 🚀 CI/CD自动化 v2.0

本项目支持完整的CI/CD自动化部署，现已升级到v2.0版本：

### 🔄 自动部署流程
1. **代码合并** → main分支触发Git post-merge钩子
2. **智能备份** → 代码和数据自动备份
3. **镜像预缓存** → Docker镜像预拉取和验证
4. **项目构建** → Maven + npm并行构建
5. **Docker部署** → 分阶段容器化部署
6. **健康监控** → 自动验证和故障恢复

### 🛡️ 多级故障恢复
- **方案1**: 完全Docker化部署（推荐）
- **方案2**: 混合模式（Docker MySQL + 本地JAR）
- **方案3**: 预构建JAR部署
- **方案4**: 最小化服务模式

### 📊 智能监控
- 实时服务状态检查
- 自动问题诊断
- 修复建议生成
- 详细诊断报告

### 🔧 部署工具
```bash
# 手动触发部署
./ci-cd/deploy/auto-deploy-trigger.sh

# 系统状态监控
./ci-cd/deploy/deployment-monitor.sh status

# 生成诊断报告
./ci-cd/deploy/deployment-monitor.sh diagnostic

# 镜像预缓存
./ci-cd/deploy/prepare-docker-images.sh
```

## 📝 更新日志

- **2025-07-06**:
  - 📚 **文档系统深度重构优化**
  - ✅ 完成根目录和docs目录的全面整理
  - ✅ 删除过时和重复的文档内容
  - ✅ 统一CHANGELOG.md到docs目录，避免重复
  - ✅ 移除过时的项目计划文件
  - ✅ 优化文档结构，提升可维护性
- **2025-07-03**: CI/CD自动化升级到v2.0，Docker化部署策略完善
- **2025-06-30**:
  - ✅ 完成项目结构深度清理和优化
  - ✅ 更新项目文档，反映最新目录结构
  - ✅ 创建详细的项目目录结构说明文档
  - ✅ 修复Redis依赖问题，系统完全正常运行
- **2025-06-23**: 重构项目结构，建立标准化文档和脚本管理体系
- **2025-06-22**: 完成Docker容器化部署和CI/CD自动化配置
- **2025-06-21**: 完成数据库迁移和多数据源配置

## 📄 许可证

本项目采用 MIT 许可证。详见 [LICENSE](LICENSE) 文件。

---

*如有疑问，请查看 [文档中心](docs/README.md) 或联系开发团队。*
